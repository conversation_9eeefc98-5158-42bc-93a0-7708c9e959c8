<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AI Forex Trading Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            background-color: #f4f6f9;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            padding: 0;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .stat-card {
            text-align: center;
            padding: 20px;
        }

        .stat-card .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-card .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .profit-positive {
            color: var(--success-color);
        }

        .profit-negative {
            color: var(--danger-color);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-connected {
            background-color: rgba(39, 174, 96, 0.2);
            color: var(--success-color);
        }

        .status-disconnected {
            background-color: rgba(231, 76, 60, 0.2);
            color: var(--danger-color);
        }

        .status-pending {
            background-color: rgba(243, 156, 18, 0.2);
            color: var(--warning-color);
        }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
            
            .main-content {
                padding: 10px;
            }
            
            .stat-card .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                AI Forex Trading
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <span id="username">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showProfile()">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSettings()">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <nav class="nav flex-column pt-3">
                    <a class="nav-link active" href="#" onclick="showDashboard(this)">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                    <a class="nav-link" href="#" onclick="showAccounts(this)">
                        <i class="fas fa-wallet"></i>
                        Accounts
                    </a>
                    <a class="nav-link" href="#" onclick="showTrades(this)">
                        <i class="fas fa-exchange-alt"></i>
                        Trades
                    </a>
                    <a class="nav-link" href="#" onclick="showSignals(this)">
                        <i class="fas fa-signal"></i>
                        Signals
                    </a>
                    <a class="nav-link" href="#" onclick="showPerformance(this)">
                        <i class="fas fa-chart-bar"></i>
                        Performance
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Dashboard Content -->
                <div id="dashboardContent">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Dashboard</h2>
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="stat-number" id="totalAccounts">-</div>
                                <div class="stat-label">Total Accounts</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="stat-number" id="totalTrades">-</div>
                                <div class="stat-label">Total Trades</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="stat-number profit-positive" id="netProfit">-</div>
                                <div class="stat-label">Net Profit</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="stat-number" id="winRate">-</div>
                                <div class="stat-label">Win Rate</div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts and Tables -->
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Performance Chart</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="performanceChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Account Status</h5>
                                </div>
                                <div class="card-body">
                                    <div id="accountsList">
                                        <div class="text-center">
                                            <div class="spinner-border" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Trades -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Trades</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Symbol</th>
                                            <th>Type</th>
                                            <th>Volume</th>
                                            <th>Open Price</th>
                                            <th>Close Price</th>
                                            <th>Profit</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recentTradesTable">
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                <div class="spinner-border" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other content sections (hidden by default) -->
                <div id="accountsContent" style="display: none;">
                    <h2>MT5 Accounts</h2>
                    <p>Account management content will be loaded here...</p>
                </div>

                <div id="tradesContent" style="display: none;">
                    <h2>Trade History</h2>
                    <p>Trade history content will be loaded here...</p>
                </div>

                <div id="signalsContent" style="display: none;">
                    <h2>Trading Signals</h2>
                    <p>Trading signals content will be loaded here...</p>
                </div>

                <div id="performanceContent" style="display: none;">
                    <h2>Performance Analysis</h2>
                    <p>Performance analysis content will be loaded here...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        let performanceChart = null;

        // API base URL
        const API_BASE = '/api';

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            if (authToken) {
                validateToken();
            } else {
                window.location.href = '/login.html';
            }
        });

        // Authentication functions
        async function validateToken() {
            try {
                const response = await fetch(`${API_BASE}/auth/validate-token`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    currentUser = data;
                    document.getElementById('username').textContent = data.email;
                    loadDashboard();
                } else {
                    localStorage.removeItem('authToken');
                    window.location.href = '/login.html';
                }
            } catch (error) {
                console.error('Token validation error:', error);
                localStorage.removeItem('authToken');
                window.location.href = '/login.html';
            }
        }

        function logout() {
            localStorage.removeItem('authToken');
            window.location.href = '/login.html';
        }

        // Navigation functions
        function setActiveNav(element) {
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            element.classList.add('active');
        }

        function hideAllContent() {
            document.getElementById('dashboardContent').style.display = 'none';
            document.getElementById('accountsContent').style.display = 'none';
            document.getElementById('tradesContent').style.display = 'none';
            document.getElementById('signalsContent').style.display = 'none';
            document.getElementById('performanceContent').style.display = 'none';
        }

        function showDashboard(element) {
            if (element) setActiveNav(element);
            hideAllContent();
            document.getElementById('dashboardContent').style.display = 'block';
            loadDashboard();
        }

        function showAccounts(element) {
            if (element) setActiveNav(element);
            hideAllContent();
            document.getElementById('accountsContent').style.display = 'block';
        }

        function showTrades(element) {
            if (element) setActiveNav(element);
            hideAllContent();
            document.getElementById('tradesContent').style.display = 'block';
        }

        function showSignals(element) {
            if (element) setActiveNav(element);
            hideAllContent();
            document.getElementById('signalsContent').style.display = 'block';
        }

        function showPerformance(element) {
            if (element) setActiveNav(element);
            hideAllContent();
            document.getElementById('performanceContent').style.display = 'block';
        }

        function showProfile() {
            // TODO: Implement profile modal
            alert('Profile functionality coming soon!');
        }

        function showSettings() {
            // TODO: Implement settings modal
            alert('Settings functionality coming soon!');
        }

        // Dashboard functions
        async function loadDashboard() {
            try {
                await Promise.all([
                    loadDashboardStats(),
                    loadAccountsList(),
                    loadRecentTrades(),
                    loadPerformanceChart()
                ]);
            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }

        async function loadDashboardStats() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/stats`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const stats = await response.json();
                    
                    document.getElementById('totalAccounts').textContent = stats.total_accounts;
                    document.getElementById('totalTrades').textContent = stats.total_trades;
                    document.getElementById('netProfit').textContent = formatCurrency(stats.net_profit);
                    document.getElementById('winRate').textContent = formatPercentage(stats.win_rate);
                    
                    // Update profit color
                    const profitElement = document.getElementById('netProfit');
                    profitElement.className = stats.net_profit >= 0 ? 'stat-number profit-positive' : 'stat-number profit-negative';
                }
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
            }
        }

        async function loadAccountsList() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/accounts-performance`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const accounts = await response.json();
                    const container = document.getElementById('accountsList');
                    
                    if (accounts.length === 0) {
                        container.innerHTML = '<p class="text-muted text-center">No accounts connected</p>';
                        return;
                    }
                    
                    container.innerHTML = accounts.map(account => `
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                            <div>
                                <strong>${account.account_name || account.login}</strong>
                                <br>
                                <small class="text-muted">Balance: ${formatCurrency(account.balance)}</small>
                            </div>
                            <span class="status-badge status-${account.status === 'connected' ? 'connected' : 'disconnected'}">
                                ${account.status}
                            </span>
                        </div>
                    `).join('');
                }
            } catch (error) {
                console.error('Error loading accounts list:', error);
                document.getElementById('accountsList').innerHTML = '<p class="text-danger">Error loading accounts</p>';
            }
        }

        async function loadRecentTrades() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/trades?limit=10`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const trades = await response.json();
                    const tbody = document.getElementById('recentTradesTable');
                    
                    if (trades.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No trades found</td></tr>';
                        return;
                    }
                    
                    tbody.innerHTML = trades.map(trade => `
                        <tr>
                            <td>${trade.symbol}</td>
                            <td>${trade.trade_type}</td>
                            <td>${trade.volume}</td>
                            <td>${trade.open_price}</td>
                            <td>${trade.close_price || '-'}</td>
                            <td class="${trade.profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                                ${trade.profit ? formatCurrency(trade.profit) : '-'}
                            </td>
                            <td>
                                <span class="status-badge status-${trade.status === 'closed' ? 'connected' : 'pending'}">
                                    ${trade.status}
                                </span>
                            </td>
                        </tr>
                    `).join('');
                }
            } catch (error) {
                console.error('Error loading recent trades:', error);
                document.getElementById('recentTradesTable').innerHTML = '<tr><td colspan="7" class="text-center text-danger">Error loading trades</td></tr>';
            }
        }

        async function loadPerformanceChart() {
            // TODO: Implement performance chart with real data
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            if (performanceChart) {
                performanceChart.destroy();
            }
            
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Profit/Loss',
                        data: [0, 150, -50, 300, 200, 450],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function refreshDashboard() {
            loadDashboard();
        }

        // Utility functions
        function formatCurrency(amount, currency = 'USD') {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }

        function formatPercentage(value) {
            return `${value.toFixed(2)}%`;
        }
    </script>
</body>
</html>
