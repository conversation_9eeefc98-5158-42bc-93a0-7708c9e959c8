#!/usr/bin/env python3
"""
Simple MetaApi connection test
"""
import asyncio
import os
from dotenv import load_dotenv
from metaapi_cloud_sdk import MetaApi

# Load environment variables explicitly
load_dotenv('.env', override=True)

async def test_metaapi_simple():
    """Simple MetaApi connection test"""
    
    print("🧪 Simple MetaApi Connection Test")
    print("=" * 50)
    
    # Get credentials from environment
    token = os.getenv('METAAPI_TOKEN')
    account_id = os.getenv('MASTER_ACCOUNT_ID')
    
    print(f"📋 Configuration:")
    print(f"   Token: {token[:50]}..." if token and len(token) > 50 else f"   Token: {token}")
    print(f"   Account ID: {account_id}")
    print()
    
    try:
        # Initialize MetaApi
        print("🔌 Initializing MetaApi...")
        api = MetaApi(token)
        
        # Get all accounts
        print("🔍 Getting accounts...")
        accounts = await api.metatrader_account_api.get_accounts_with_infinite_scroll_pagination()
        
        print(f"✅ Found {len(accounts)} account(s)")
        
        for account in accounts:
            print(f"   Account ID: {account.id}")
            print(f"   Login: {account.login}")
            print(f"   Server: {account.server}")
            print(f"   Name: {account.name}")
            print(f"   State: {account.state}")
            print(f"   Platform: {getattr(account, 'platform', 'MT5')}")
            print()
            
            if account.login == account_id:
                print(f"✅ Found your trading account!")
                print(f"   Account is {'DEPLOYED' if account.state == 'DEPLOYED' else 'NOT DEPLOYED'}")
                
                # Test basic connection
                print("🔗 Testing basic connection...")
                connection = account.get_streaming_connection()
                
                try:
                    await connection.connect()
                    print("✅ Successfully connected to MetaApi!")
                    
                    print("⏳ Testing synchronization (5 seconds)...")
                    await asyncio.sleep(5)  # Wait a bit for sync
                    
                    # Try to get terminal state
                    try:
                        terminal_state = connection.terminal_state
                        if terminal_state:
                            print("✅ Terminal state accessible")
                            if hasattr(terminal_state, 'account_information'):
                                account_info = terminal_state.account_information
                                if account_info:
                                    print(f"   Balance: ${account_info.get('balance', 0):,.2f}")
                                    print(f"   Equity: ${account_info.get('equity', 0):,.2f}")
                                    print(f"   Currency: {account_info.get('currency', 'N/A')}")
                        else:
                            print("⚠️ Terminal state not yet available")
                    except Exception as e:
                        print(f"⚠️ Terminal state access: {str(e)}")
                    
                    await connection.close()
                    
                    print()
                    print("🎉 MetaApi Connection Test SUCCESSFUL!")
                    print("✅ Your account is ready for AI trading")
                    
                    return True
                    
                except Exception as conn_error:
                    print(f"❌ Connection failed: {str(conn_error)}")
                    return False
        
        if not any(acc.login == account_id for acc in accounts):
            print(f"❌ Account with login {account_id} not found")
            print("Available accounts:")
            for acc in accounts:
                print(f"   - Login: {acc.login}, Server: {acc.server}")
            return False
            
    except Exception as e:
        print(f"❌ MetaApi test failed: {str(e)}")
        return False

if __name__ == "__main__":
    asyncio.run(test_metaapi_simple())
