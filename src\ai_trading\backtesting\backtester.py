"""
Backtesting framework for AI trading strategies
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass

from src.ai_trading.models.signal_generator import SignalGenerator
from src.ai_trading.models.feature_extractor import FeatureExtractor
from src.ai_trading.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class BacktestConfig:
    """Configuration for backtesting"""
    initial_balance: float = 10000.0
    risk_percent: float = 2.0  # Risk per trade as percentage of balance
    min_confidence: float = 0.6
    max_positions: int = 5
    commission: float = 0.0001  # 0.01% commission
    spread: float = 0.0002  # 0.02% spread
    slippage: float = 0.0001  # 0.01% slippage


@dataclass
class Trade:
    """Individual trade record"""
    entry_time: datetime
    exit_time: Optional[datetime]
    symbol: str
    action: str  # 'buy' or 'sell'
    volume: float
    entry_price: float
    exit_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    pnl: Optional[float]
    confidence: float
    status: str  # 'open', 'closed', 'stopped'


class Backtester:
    """Backtesting engine for trading strategies"""
    
    def __init__(self, config: BacktestConfig = None):
        self.config = config or BacktestConfig()
        self.signal_generator = SignalGenerator()
        self.feature_extractor = FeatureExtractor()
        
        # Backtesting state
        self.balance = self.config.initial_balance
        self.equity = self.config.initial_balance
        self.trades: List[Trade] = []
        self.open_positions: Dict[str, Trade] = {}
        self.equity_curve: List[Tuple[datetime, float]] = []
        
        # Performance metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.max_drawdown = 0.0
        self.max_equity = self.config.initial_balance
    
    def run_backtest(self, market_data: Dict[str, pd.DataFrame], 
                    start_date: datetime = None, 
                    end_date: datetime = None) -> Dict[str, Any]:
        """
        Run backtest on historical market data
        
        Args:
            market_data: Dictionary of symbol -> OHLCV DataFrame
            start_date: Start date for backtesting
            end_date: End date for backtesting
            
        Returns:
            Backtest results and performance metrics
        """
        try:
            logger.info("Starting backtest...")
            logger.info(f"Initial balance: ${self.config.initial_balance:,.2f}")
            logger.info(f"Symbols: {list(market_data.keys())}")
            
            # Reset state
            self._reset_state()
            
            # Get date range
            all_dates = set()
            for symbol, data in market_data.items():
                if not data.empty:
                    all_dates.update(data.index)
            
            all_dates = sorted(all_dates)
            
            if start_date:
                all_dates = [d for d in all_dates if d >= start_date]
            if end_date:
                all_dates = [d for d in all_dates if d <= end_date]
            
            logger.info(f"Backtesting period: {all_dates[0]} to {all_dates[-1]}")
            logger.info(f"Total time points: {len(all_dates)}")
            
            # Run backtest for each time point
            for i, current_time in enumerate(all_dates):
                try:
                    self._process_timepoint(current_time, market_data)
                    
                    # Update equity curve
                    self.equity_curve.append((current_time, self.equity))
                    
                    # Progress logging
                    if i % 1000 == 0:
                        progress = (i / len(all_dates)) * 100
                        logger.info(f"Progress: {progress:.1f}% - Equity: ${self.equity:,.2f}")
                
                except Exception as e:
                    logger.error(f"Error processing timepoint {current_time}: {e}")
                    continue
            
            # Calculate final results
            results = self._calculate_results()
            
            logger.info("Backtest completed!")
            logger.info(f"Final equity: ${self.equity:,.2f}")
            logger.info(f"Total return: {results['total_return']:.2f}%")
            logger.info(f"Total trades: {self.total_trades}")
            logger.info(f"Win rate: {results['win_rate']:.2f}%")
            
            return results
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            raise
    
    def _reset_state(self):
        """Reset backtesting state"""
        self.balance = self.config.initial_balance
        self.equity = self.config.initial_balance
        self.trades = []
        self.open_positions = {}
        self.equity_curve = []
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.max_drawdown = 0.0
        self.max_equity = self.config.initial_balance
    
    def _process_timepoint(self, current_time: datetime, market_data: Dict[str, pd.DataFrame]):
        """Process a single timepoint in the backtest"""
        try:
            # Update open positions
            self._update_open_positions(current_time, market_data)
            
            # Generate new signals
            for symbol, data in market_data.items():
                if symbol in self.open_positions:
                    continue  # Skip if already have position
                
                if len(self.open_positions) >= self.config.max_positions:
                    continue  # Max positions reached
                
                # Get data up to current time
                historical_data = data[data.index <= current_time].tail(100)
                
                if len(historical_data) < 50:  # Need enough data for indicators
                    continue
                
                # Generate signal
                signal = self.signal_generator.generate_signal(historical_data, symbol)
                
                # Check if signal is actionable
                if (signal['action'] in ['buy', 'sell'] and 
                    signal['confidence'] >= self.config.min_confidence):
                    
                    self._open_position(signal, symbol, current_time, historical_data)
            
            # Update equity and drawdown
            self._update_equity(current_time, market_data)
            
        except Exception as e:
            logger.error(f"Error processing timepoint: {e}")
    
    def _open_position(self, signal: Dict[str, Any], symbol: str, 
                      current_time: datetime, market_data: pd.DataFrame):
        """Open a new position based on signal"""
        try:
            # Get current price
            current_price = market_data.iloc[-1]['close']
            
            # Apply spread and slippage
            if signal['action'] == 'buy':
                entry_price = current_price * (1 + self.config.spread + self.config.slippage)
            else:
                entry_price = current_price * (1 - self.config.spread - self.config.slippage)
            
            # Calculate position size
            volume = self._calculate_position_size(signal, entry_price)
            
            if volume <= 0:
                return
            
            # Create trade
            trade = Trade(
                entry_time=current_time,
                exit_time=None,
                symbol=symbol,
                action=signal['action'],
                volume=volume,
                entry_price=entry_price,
                exit_price=None,
                stop_loss=signal.get('stop_loss'),
                take_profit=signal.get('take_profit'),
                pnl=None,
                confidence=signal['confidence'],
                status='open'
            )
            
            # Add to open positions
            self.open_positions[symbol] = trade
            self.total_trades += 1
            
            logger.debug(f"Opened {signal['action']} position for {symbol} at {entry_price:.4f}")
            
        except Exception as e:
            logger.error(f"Error opening position: {e}")
    
    def _update_open_positions(self, current_time: datetime, market_data: Dict[str, pd.DataFrame]):
        """Update open positions and check for exits"""
        positions_to_close = []
        
        for symbol, trade in self.open_positions.items():
            if symbol not in market_data:
                continue
            
            # Get current data
            current_data = market_data[symbol]
            current_row = current_data[current_data.index <= current_time]
            
            if current_row.empty:
                continue
            
            current_price = current_row.iloc[-1]['close']
            
            # Check for stop loss or take profit
            should_close = False
            exit_reason = None
            
            if trade.action == 'buy':
                if trade.stop_loss and current_price <= trade.stop_loss:
                    should_close = True
                    exit_reason = 'stop_loss'
                elif trade.take_profit and current_price >= trade.take_profit:
                    should_close = True
                    exit_reason = 'take_profit'
            else:  # sell
                if trade.stop_loss and current_price >= trade.stop_loss:
                    should_close = True
                    exit_reason = 'stop_loss'
                elif trade.take_profit and current_price <= trade.take_profit:
                    should_close = True
                    exit_reason = 'take_profit'
            
            # Check for time-based exit (optional)
            time_in_trade = (current_time - trade.entry_time).total_seconds() / 3600  # hours
            if time_in_trade > 24:  # Close after 24 hours
                should_close = True
                exit_reason = 'time_exit'
            
            if should_close:
                self._close_position(trade, current_price, current_time, exit_reason)
                positions_to_close.append(symbol)
        
        # Remove closed positions
        for symbol in positions_to_close:
            del self.open_positions[symbol]
    
    def _close_position(self, trade: Trade, exit_price: float, 
                       exit_time: datetime, reason: str):
        """Close a position and calculate P&L"""
        try:
            # Apply spread and slippage
            if trade.action == 'buy':
                final_exit_price = exit_price * (1 - self.config.spread - self.config.slippage)
            else:
                final_exit_price = exit_price * (1 + self.config.spread + self.config.slippage)
            
            # Calculate P&L
            if trade.action == 'buy':
                pnl = (final_exit_price - trade.entry_price) * trade.volume
            else:
                pnl = (trade.entry_price - final_exit_price) * trade.volume
            
            # Apply commission
            commission = (trade.entry_price + final_exit_price) * trade.volume * self.config.commission
            pnl -= commission
            
            # Update trade
            trade.exit_time = exit_time
            trade.exit_price = final_exit_price
            trade.pnl = pnl
            trade.status = reason
            
            # Update balance
            self.balance += pnl
            
            # Update statistics
            if pnl > 0:
                self.winning_trades += 1
            else:
                self.losing_trades += 1
            
            # Add to completed trades
            self.trades.append(trade)
            
            logger.debug(f"Closed {trade.action} position for {trade.symbol}: P&L = ${pnl:.2f}")
            
        except Exception as e:
            logger.error(f"Error closing position: {e}")
    
    def _calculate_position_size(self, signal: Dict[str, Any], entry_price: float) -> float:
        """Calculate position size based on risk management"""
        try:
            # Risk amount
            risk_amount = self.balance * (self.config.risk_percent / 100)
            
            # Calculate position size based on stop loss
            stop_loss = signal.get('stop_loss')
            if stop_loss:
                stop_distance = abs(entry_price - stop_loss)
                if stop_distance > 0:
                    volume = risk_amount / stop_distance
                    return max(0.01, min(volume, self.balance * 0.1))  # Max 10% of balance
            
            # Fallback to fixed percentage
            return self.balance * 0.02  # 2% of balance
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0
    
    def _update_equity(self, current_time: datetime, market_data: Dict[str, pd.DataFrame]):
        """Update current equity including unrealized P&L"""
        try:
            unrealized_pnl = 0.0
            
            for symbol, trade in self.open_positions.items():
                if symbol in market_data:
                    current_data = market_data[symbol]
                    current_row = current_data[current_data.index <= current_time]
                    
                    if not current_row.empty:
                        current_price = current_row.iloc[-1]['close']
                        
                        # Calculate unrealized P&L
                        if trade.action == 'buy':
                            unrealized = (current_price - trade.entry_price) * trade.volume
                        else:
                            unrealized = (trade.entry_price - current_price) * trade.volume
                        
                        unrealized_pnl += unrealized
            
            # Update equity
            self.equity = self.balance + unrealized_pnl
            
            # Update max equity and drawdown
            if self.equity > self.max_equity:
                self.max_equity = self.equity
            
            current_drawdown = (self.max_equity - self.equity) / self.max_equity
            if current_drawdown > self.max_drawdown:
                self.max_drawdown = current_drawdown
            
        except Exception as e:
            logger.error(f"Error updating equity: {e}")
    
    def _calculate_results(self) -> Dict[str, Any]:
        """Calculate final backtest results"""
        try:
            # Basic metrics
            total_return = ((self.equity - self.config.initial_balance) / self.config.initial_balance) * 100
            win_rate = (self.winning_trades / max(1, self.total_trades)) * 100
            
            # P&L statistics
            pnls = [trade.pnl for trade in self.trades if trade.pnl is not None]
            
            if pnls:
                avg_win = np.mean([pnl for pnl in pnls if pnl > 0]) if self.winning_trades > 0 else 0
                avg_loss = np.mean([pnl for pnl in pnls if pnl < 0]) if self.losing_trades > 0 else 0
                profit_factor = abs(sum([pnl for pnl in pnls if pnl > 0]) / 
                                  max(1, abs(sum([pnl for pnl in pnls if pnl < 0]))))
            else:
                avg_win = avg_loss = profit_factor = 0
            
            # Time-based metrics
            if self.equity_curve:
                start_date = self.equity_curve[0][0]
                end_date = self.equity_curve[-1][0]
                days = (end_date - start_date).days
                annual_return = (total_return / max(1, days)) * 365 if days > 0 else 0
            else:
                annual_return = 0
            
            return {
                'initial_balance': self.config.initial_balance,
                'final_equity': self.equity,
                'total_return': total_return,
                'annual_return': annual_return,
                'max_drawdown': self.max_drawdown * 100,
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'losing_trades': self.losing_trades,
                'win_rate': win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'equity_curve': self.equity_curve,
                'trades': self.trades
            }
            
        except Exception as e:
            logger.error(f"Error calculating results: {e}")
            return {}
    
    def plot_results(self, results: Dict[str, Any], save_path: str = None):
        """Plot backtest results"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Backtest Results', fontsize=16)
            
            # Equity curve
            if results.get('equity_curve'):
                dates, equity = zip(*results['equity_curve'])
                axes[0, 0].plot(dates, equity, linewidth=2)
                axes[0, 0].set_title('Equity Curve')
                axes[0, 0].set_ylabel('Equity ($)')
                axes[0, 0].grid(True)
            
            # P&L distribution
            if results.get('trades'):
                pnls = [trade.pnl for trade in results['trades'] if trade.pnl is not None]
                if pnls:
                    axes[0, 1].hist(pnls, bins=30, alpha=0.7, edgecolor='black')
                    axes[0, 1].set_title('P&L Distribution')
                    axes[0, 1].set_xlabel('P&L ($)')
                    axes[0, 1].set_ylabel('Frequency')
                    axes[0, 1].grid(True)
            
            # Monthly returns (if enough data)
            if results.get('equity_curve') and len(results['equity_curve']) > 30:
                # Simplified monthly returns calculation
                equity_df = pd.DataFrame(results['equity_curve'], columns=['date', 'equity'])
                equity_df.set_index('date', inplace=True)
                monthly_returns = equity_df.resample('M')['equity'].last().pct_change().dropna() * 100
                
                if not monthly_returns.empty:
                    axes[1, 0].bar(range(len(monthly_returns)), monthly_returns.values)
                    axes[1, 0].set_title('Monthly Returns')
                    axes[1, 0].set_ylabel('Return (%)')
                    axes[1, 0].grid(True)
            
            # Performance metrics table
            metrics_text = f"""
            Total Return: {results.get('total_return', 0):.2f}%
            Annual Return: {results.get('annual_return', 0):.2f}%
            Max Drawdown: {results.get('max_drawdown', 0):.2f}%
            Win Rate: {results.get('win_rate', 0):.2f}%
            Total Trades: {results.get('total_trades', 0)}
            Profit Factor: {results.get('profit_factor', 0):.2f}
            """
            
            axes[1, 1].text(0.1, 0.5, metrics_text, fontsize=12, 
                           verticalalignment='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Performance Metrics')
            axes[1, 1].axis('off')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"Results plot saved to {save_path}")
            
            plt.show()
            
        except Exception as e:
            logger.error(f"Error plotting results: {e}")
