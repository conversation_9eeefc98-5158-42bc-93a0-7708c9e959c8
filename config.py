"""
Configuration management for the AI Forex Trading Platform
"""
import os
from typing import Optional
from pydantic import BaseSettings, validator
from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # MetaApi Configuration
    metaapi_token: str
    master_account_id: str
    master_account_password: str
    master_account_server: str = "ThinkMarkets-Demo"
    
    # Database Configuration
    database_url: str
    database_host: str = "localhost"
    database_port: int = 5432
    database_name: str = "forex_trading_db"
    database_user: Optional[str] = None
    database_password: Optional[str] = None

    @validator('database_url', pre=True)
    def validate_database_url(cls, v):
        """Ensure database URL is properly formatted"""
        if not v:
            raise ValueError("DATABASE_URL is required")

        # Convert postgresql:// to postgresql+asyncpg:// if needed
        if v.startswith('postgresql://'):
            v = v.replace('postgresql://', 'postgresql+asyncpg://', 1)
        elif not v.startswith('postgresql+asyncpg://'):
            raise ValueError("DATABASE_URL must start with postgresql+asyncpg://")

        return v
    
    # Security
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Trading Configuration
    default_risk_percent: float = 1.0
    max_concurrent_trades: int = 5
    volatility_threshold: float = 0.7
    
    # AI Model Configuration
    model_update_interval_hours: int = 24
    feature_lookback_periods: int = 20
    training_data_days: int = 365
    
    # Logging
    log_level: str = "INFO"
    log_file_path: str = "logs/trading_bot.log"
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_reload: bool = True
    
    # Notification Settings
    telegram_bot_token: Optional[str] = None
    telegram_chat_id: Optional[str] = None
    email_smtp_server: Optional[str] = None
    email_smtp_port: Optional[int] = None
    email_username: Optional[str] = None
    email_password: Optional[str] = None
    
    # Development Settings
    environment: str = "development"
    debug: bool = True
    
    @validator('volatility_threshold')
    def validate_volatility_threshold(cls, v):
        if not 0 <= v <= 1:
            raise ValueError('Volatility threshold must be between 0 and 1')
        return v
    
    @validator('default_risk_percent')
    def validate_risk_percent(cls, v):
        if not 0 < v <= 10:
            raise ValueError('Risk percent must be between 0 and 10')
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


class TradingConfig:
    """Trading-specific configuration constants"""
    
    # Supported symbols
    SUPPORTED_SYMBOLS = [
        "XAUUSD",  # Gold
        "EURUSD",  # Euro/USD
        "GBPUSD",  # Pound/USD
        "USDJPY",  # USD/Yen
        "USDCHF",  # USD/Swiss Franc
        "AUDUSD",  # Australian Dollar/USD
        "USDCAD",  # USD/Canadian Dollar
    ]
    
    # Timeframes for analysis
    TIMEFRAMES = {
        "M1": "1m",
        "M5": "5m", 
        "M15": "15m",
        "M30": "30m",
        "H1": "1h",
        "H4": "4h",
        "D1": "1d"
    }
    
    # Default timeframe for signal generation
    DEFAULT_TIMEFRAME = "M5"
    
    # Risk management
    MAX_DRAWDOWN_PERCENT = 20.0
    STOP_LOSS_MULTIPLIER = 2.0  # ATR multiplier for stop loss
    TAKE_PROFIT_MULTIPLIER = 3.0  # ATR multiplier for take profit
    
    # AI Model parameters
    VOLATILITY_FEATURES = [
        "atr_14",
        "bb_width", 
        "std_dev_20",
        "volume_avg_10",
        "rsi_14",
        "hour_of_day",
        "price_distance_to_ma"
    ]


class DatabaseConfig:
    """Database configuration and table names"""
    
    # Table names
    USERS_TABLE = "users"
    ACCOUNTS_TABLE = "mt5_accounts" 
    TRADES_TABLE = "trades"
    SIGNALS_TABLE = "trading_signals"
    MODELS_TABLE = "ai_models"
    
    # Connection pool settings
    POOL_SIZE = 10
    MAX_OVERFLOW = 20
    POOL_TIMEOUT = 30
    POOL_RECYCLE = 3600
