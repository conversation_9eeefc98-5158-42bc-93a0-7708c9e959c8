"""
Authentication utilities for the AI trading platform
"""
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from src.ai_trading.database.database import DatabaseManager
from src.ai_trading.database.models import User
from config import settings
from src.ai_trading.utils.logger import get_logger

logger = get_logger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token security
security = HTTPBearer()


class AuthManager:
    """Authentication and authorization manager"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Hash a password"""
        return pwd_context.hash(password)
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            return payload
        except JWTError as e:
            logger.warning(f"Token verification failed: {e}")
            return None
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password"""
        try:
            user = await self.db_manager.get_user_by_email(email)
            if not user:
                return None
            
            if not self.verify_password(password, user.hashed_password):
                return None
            
            if not user.is_active:
                return None
            
            return user
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    async def register_user(self, username: str, email: str, password: str, 
                          full_name: Optional[str] = None) -> Optional[User]:
        """Register a new user"""
        try:
            # Check if user already exists
            existing_user = await self.db_manager.get_user_by_email(email)
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
            
            existing_username = await self.db_manager.get_user_by_username(username)
            if existing_username:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already taken"
                )
            
            # Hash password
            hashed_password = self.get_password_hash(password)
            
            # Create user
            user = await self.db_manager.create_user(
                username=username,
                email=email,
                hashed_password=hashed_password,
                full_name=full_name
            )
            
            logger.info(f"New user registered: {email}")
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"User registration error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Registration failed"
            )
    
    async def get_current_user(self, token: str) -> User:
        """Get current user from JWT token"""
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
        try:
            payload = self.verify_token(token)
            if payload is None:
                raise credentials_exception
            
            user_id: int = payload.get("sub")
            if user_id is None:
                raise credentials_exception
                
        except JWTError:
            raise credentials_exception
        
        user = await self.db_manager.get_user(user_id=int(user_id))
        if user is None:
            raise credentials_exception
        
        return user
    
    def generate_verification_token(self, user_id: int) -> str:
        """Generate email verification token"""
        data = {
            "user_id": user_id,
            "type": "email_verification",
            "exp": datetime.utcnow() + timedelta(hours=24)
        }
        return jwt.encode(data, settings.secret_key, algorithm=settings.algorithm)
    
    def verify_email_token(self, token: str) -> Optional[int]:
        """Verify email verification token"""
        try:
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            if payload.get("type") != "email_verification":
                return None
            return payload.get("user_id")
        except JWTError:
            return None
    
    def generate_password_reset_token(self, user_id: int) -> str:
        """Generate password reset token"""
        data = {
            "user_id": user_id,
            "type": "password_reset",
            "exp": datetime.utcnow() + timedelta(hours=1)
        }
        return jwt.encode(data, settings.secret_key, algorithm=settings.algorithm)
    
    def verify_password_reset_token(self, token: str) -> Optional[int]:
        """Verify password reset token"""
        try:
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            if payload.get("type") != "password_reset":
                return None
            return payload.get("user_id")
        except JWTError:
            return None


# Global auth manager instance
auth_manager = AuthManager()


# Dependency functions for FastAPI
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """FastAPI dependency to get current authenticated user"""
    return await auth_manager.get_current_user(credentials.credentials)


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """FastAPI dependency to get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


def validate_password_strength(password: str) -> bool:
    """Validate password strength"""
    if len(password) < 8:
        return False
    
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
    
    return has_upper and has_lower and has_digit and has_special


def generate_secure_token() -> str:
    """Generate a secure random token"""
    return secrets.token_urlsafe(32)


class PasswordValidator:
    """Password validation utilities"""
    
    @staticmethod
    def validate(password: str) -> Dict[str, Any]:
        """Validate password and return detailed feedback"""
        result = {
            "valid": True,
            "errors": [],
            "score": 0
        }
        
        # Length check
        if len(password) < 8:
            result["valid"] = False
            result["errors"].append("Password must be at least 8 characters long")
        else:
            result["score"] += 1
        
        # Character type checks
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        if not has_upper:
            result["valid"] = False
            result["errors"].append("Password must contain at least one uppercase letter")
        else:
            result["score"] += 1
        
        if not has_lower:
            result["valid"] = False
            result["errors"].append("Password must contain at least one lowercase letter")
        else:
            result["score"] += 1
        
        if not has_digit:
            result["valid"] = False
            result["errors"].append("Password must contain at least one digit")
        else:
            result["score"] += 1
        
        if not has_special:
            result["valid"] = False
            result["errors"].append("Password must contain at least one special character")
        else:
            result["score"] += 1
        
        # Common password check
        common_passwords = [
            "password", "123456", "password123", "admin", "qwerty",
            "letmein", "welcome", "monkey", "dragon", "master"
        ]
        
        if password.lower() in common_passwords:
            result["valid"] = False
            result["errors"].append("Password is too common")
            result["score"] = max(0, result["score"] - 2)
        
        # Calculate strength
        if result["score"] >= 5:
            result["strength"] = "strong"
        elif result["score"] >= 3:
            result["strength"] = "medium"
        else:
            result["strength"] = "weak"
        
        return result


class SessionManager:
    """Session management for web dashboard"""
    
    def __init__(self):
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
    
    def create_session(self, user_id: int) -> str:
        """Create a new session"""
        session_id = generate_secure_token()
        self.active_sessions[session_id] = {
            "user_id": user_id,
            "created_at": datetime.utcnow(),
            "last_activity": datetime.utcnow()
        }
        return session_id
    
    def validate_session(self, session_id: str) -> Optional[int]:
        """Validate session and return user_id"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        # Check if session expired
        if datetime.utcnow() - session["last_activity"] > timedelta(minutes=settings.session_timeout_minutes):
            del self.active_sessions[session_id]
            return None
        
        # Update last activity
        session["last_activity"] = datetime.utcnow()
        return session["user_id"]
    
    def destroy_session(self, session_id: str):
        """Destroy a session"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        current_time = datetime.utcnow()
        expired_sessions = []
        
        for session_id, session in self.active_sessions.items():
            if current_time - session["last_activity"] > timedelta(minutes=settings.session_timeout_minutes):
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.active_sessions[session_id]


# Global session manager
session_manager = SessionManager()
