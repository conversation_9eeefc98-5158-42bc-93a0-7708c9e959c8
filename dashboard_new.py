from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn
import asyncio
import random
from datetime import datetime, timedelta

app = FastAPI()

class BacktestRequest(BaseModel):
    symbol: str
    timeframe: str
    period_days: int
    strategy: str
    initial_balance: float = 10000.0

# Mock data for demonstration
mock_account_data = {
    "balance": 10000.00,
    "equity": 10250.00,
    "margin": 150.00,
    "free_margin": 10100.00,
    "positions": 3
}

@app.get("/api/account")
async def get_account_info():
    """Get current account information"""
    return {
        "success": True,
        "data": mock_account_data
    }

@app.post("/api/backtest")
async def run_backtest(request: BacktestRequest):
    """Run backtest with specified parameters"""
    try:
        # Simulate backtest processing
        await asyncio.sleep(2)  # Simulate processing time

        # Generate mock results based on strategy
        strategy_multipliers = {
            "ai_signals": 1.2,
            "trend_following": 0.8,
            "mean_reversion": 0.6,
            "momentum": 1.0
        }

        base_return = random.uniform(-10, 25)
        multiplier = strategy_multipliers.get(request.strategy, 1.0)
        total_return = base_return * multiplier

        win_rate = random.uniform(60, 85)
        total_trades = random.randint(50, 200)
        max_drawdown = random.uniform(-15, -2)
        profit_factor = random.uniform(1.2, 2.5)
        final_balance = request.initial_balance * (1 + total_return / 100)

        return {
            "success": True,
            "total_return": round(total_return, 2),
            "win_rate": round(win_rate, 1),
            "total_trades": total_trades,
            "max_drawdown": round(max_drawdown, 2),
            "profit_factor": round(profit_factor, 2),
            "final_balance": round(final_balance, 2),
            "strategy": request.strategy,
            "symbol": request.symbol,
            "period_days": request.period_days
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/")
async def dashboard():
    return HTMLResponse("""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Forex Trading Platform - Professional Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0a0e1a;
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #1a1f3a 0%, #2d3561 100%);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: #00d4ff;
        }
        
        .status-bar {
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff88;
            animation: pulse 2s infinite;
        }
        
        /* Navigation Tabs */
        .nav-tabs {
            background: #1a1f3a;
            padding: 0 2rem;
            display: flex;
            gap: 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .nav-tab {
            padding: 1rem 2rem;
            background: none;
            border: none;
            color: #8892b0;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .nav-tab:hover {
            color: #00d4ff;
            background: rgba(0, 212, 255, 0.05);
        }
        
        .nav-tab.active {
            color: #00d4ff;
            border-bottom-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
        
        /* Main Container */
        .main-container {
            padding: 2rem;
            max-width: 1600px;
            margin: 0 auto;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        /* Cards */
        .card {
            background: linear-gradient(135deg, #1e2749 0%, #2a3284 100%);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(255,255,255,0.1);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.4);
            border-color: rgba(0, 212, 255, 0.3);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
        }
        
        .card-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 600;
            color: #ffffff;
        }
        
        .card-icon {
            width: 24px;
            height: 24px;
            color: #00d4ff;
        }
        
        /* Account Overview */
        .account-overview {
            grid-column: span 3;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .account-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: rgba(255,255,255,0.8);
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .stat-change {
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }
        
        .stat-change.positive {
            color: #00ff88;
        }
        
        .stat-change.negative {
            color: #ff4757;
        }

        /* Autotrade Styles */
        .autotrade-control {
            grid-column: span 2;
        }

        .autotrade-toggle .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .autotrade-toggle .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .autotrade-toggle .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .autotrade-toggle .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .autotrade-toggle input:checked + .slider {
            background-color: #00ff88;
        }

        .autotrade-toggle input:checked + .slider:before {
            transform: translateX(26px);
        }

        .autotrade-details {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
        }

        .detail-row .label {
            font-weight: 600;
            color: #8892b0;
        }

        .strategy-badge {
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .signal-display {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .signal-symbol {
            background: rgba(255,255,255,0.1);
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .signal-type {
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .signal-type.buy {
            background: #00ff88;
            color: #000;
        }

        .signal-type.sell {
            background: #ff4757;
            color: #fff;
        }

        .signal-time {
            font-size: 0.8rem;
            color: #8892b0;
        }

        .prediction-display {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .confidence {
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .confidence.high {
            background: #00ff88;
            color: #000;
        }

        .confidence.medium {
            background: #ffa502;
            color: #000;
        }

        .confidence.low {
            background: #ff4757;
            color: #fff;
        }

        .risk-selector, .trade-size-input input {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #ffffff;
            padding: 0.5rem;
            border-radius: 6px;
        }

        .trade-size-input {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .trade-size-input input {
            width: 80px;
        }

        .trade-size-input .unit {
            color: #8892b0;
            font-size: 0.9rem;
        }

        /* Performance Metrics */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .metric-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #8892b0;
        }

        /* Recent Trades */
        .trades-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .trade-item {
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: 1rem;
            align-items: center;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
        }

        .trade-symbol {
            font-weight: 600;
        }

        .trade-profit.positive {
            color: #00ff88;
        }

        .trade-profit.negative {
            color: #ff4757;
        }

        /* Backtest Styles */
        .backtest-config {
            grid-column: span 1;
        }

        .config-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: 600;
            color: #8892b0;
        }

        .form-group select,
        .form-group input {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #ffffff;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 1rem;
        }

        .backtest-btn {
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #000;
            border: none;
            padding: 1rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .backtest-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }

        .backtest-results {
            grid-column: span 2;
        }

        .no-results {
            text-align: center;
            padding: 3rem;
            color: #8892b0;
        }

        .no-results i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .result-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
        }

        .result-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .result-label {
            font-size: 0.9rem;
            color: #8892b0;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #00d4ff;
        }

        .loading i {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .error {
            text-align: center;
            padding: 2rem;
            color: #ff4757;
            background: rgba(255, 71, 87, 0.1);
            border-radius: 8px;
        }

        /* Strategy Performance */
        .performance-comparison {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .strategy-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
        }

        .strategy-name {
            font-weight: 600;
        }

        .strategy-return {
            font-weight: 700;
            font-size: 1.1rem;
        }

        .strategy-trades {
            font-size: 0.9rem;
            color: #8892b0;
        }

        /* AI Info Styles */
        .ai-overview {
            grid-column: span 2;
        }

        .ai-description h4 {
            color: #00d4ff;
            margin-bottom: 1rem;
        }

        .ai-description p {
            color: #8892b0;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .ai-features {
            list-style: none;
            padding: 0;
        }

        .ai-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            color: #ffffff;
        }

        .ai-features li:last-child {
            border-bottom: none;
        }

        .ai-features strong {
            color: #00d4ff;
        }

        /* Signal Process */
        .process-steps {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #000;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            flex-shrink: 0;
        }

        .step-content h5 {
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }

        .step-content p {
            color: #8892b0;
            font-size: 0.9rem;
        }

        /* Model Performance Stats */
        .performance-stats {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
        }

        .stat-name {
            color: #8892b0;
            font-weight: 500;
        }

        .stat-value {
            color: #ffffff;
            font-weight: 600;
        }

        /* Technical Indicators */
        .indicators-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .indicator-item {
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            text-align: center;
        }

        .indicator-name {
            font-size: 0.9rem;
            color: #8892b0;
            margin-bottom: 0.5rem;
        }

        .indicator-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 0.5rem;
        }

        .indicator-status {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .indicator-status.bullish {
            background: #00ff88;
            color: #000;
        }

        .indicator-status.bearish {
            background: #ff4757;
            color: #fff;
        }

        .indicator-status.neutral {
            background: #8892b0;
            color: #000;
        }

        /* Risk Management */
        .risk-details {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .risk-item {
            padding: 1rem;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
        }

        .risk-item h5 {
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }

        .risk-item p {
            color: #8892b0;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* Model Updates */
        .update-info {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .update-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
        }

        .update-date {
            color: #8892b0;
            font-weight: 500;
        }

        .update-value {
            color: #ffffff;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr 1fr;
            }

            .account-overview {
                grid-column: span 2;
            }

            .autotrade-control {
                grid-column: span 2;
            }

            .backtest-results {
                grid-column: span 2;
            }

            .ai-overview {
                grid-column: span 2;
            }
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .account-overview,
            .autotrade-control,
            .backtest-results,
            .ai-overview {
                grid-column: span 1;
            }

            .account-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .results-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .indicators-grid {
                grid-template-columns: 1fr;
            }
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">
            <i class="fas fa-robot"></i>
            AI Forex Trading Platform
        </div>
        <div class="status-bar">
            <div class="connection-status">
                <div class="status-dot"></div>
                <span>MetaApi Connected</span>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showTab('dashboard')">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </button>
        <button class="nav-tab" onclick="showTab('autotrade')">
            <i class="fas fa-robot"></i> Autotrade
        </button>
        <button class="nav-tab" onclick="showTab('backtest')">
            <i class="fas fa-chart-line"></i> Backtest
        </button>
        <button class="nav-tab" onclick="showTab('ai-info')">
            <i class="fas fa-brain"></i> AI Info
        </button>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <div class="dashboard-grid">
                <!-- Account Overview -->
                <div class="card account-overview">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-wallet card-icon"></i>
                            Account Overview
                        </div>
                    </div>
                    <div class="account-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="balance">$10,000.00</div>
                            <div class="stat-label">Balance</div>
                            <div class="stat-change positive">+2.5%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="equity">$10,250.00</div>
                            <div class="stat-label">Equity</div>
                            <div class="stat-change positive">+2.5%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="margin">$150.00</div>
                            <div class="stat-label">Used Margin</div>
                            <div class="stat-change">1.5%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="free-margin">$10,100.00</div>
                            <div class="stat-label">Free Margin</div>
                            <div class="stat-change positive">98.5%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="positions">3</div>
                            <div class="stat-label">Open Positions</div>
                            <div class="stat-change">Active</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Autotrade Tab -->
        <div id="autotrade" class="tab-content">
            <div class="dashboard-grid">
                <!-- Autotrade Control -->
                <div class="card autotrade-control">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-robot card-icon"></i>
                            Autotrade Control
                        </div>
                        <div class="autotrade-toggle">
                            <label class="switch">
                                <input type="checkbox" id="autotrade-enabled" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="autotrade-details">
                        <div class="detail-row">
                            <span class="label">Bot Strategy:</span>
                            <span class="value strategy-badge">EURUSD Trend AI</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Last Signal:</span>
                            <div class="signal-display">
                                <span class="signal-symbol">EURUSD</span>
                                <span class="signal-type buy">BUY</span>
                                <span class="signal-time">2 min ago</span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <span class="label">Next Prediction:</span>
                            <div class="prediction-display">
                                <span class="confidence high">85% confidence</span>
                                <span class="target">Target: 1.0950</span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <span class="label">Risk Profile:</span>
                            <select class="risk-selector">
                                <option value="conservative">Conservative</option>
                                <option value="balanced" selected>Balanced</option>
                                <option value="aggressive">Aggressive</option>
                            </select>
                        </div>
                        <div class="detail-row">
                            <span class="label">Max Trade Size:</span>
                            <div class="trade-size-input">
                                <input type="number" value="0.1" step="0.01" min="0.01" max="10.0">
                                <span class="unit">lots</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="card performance-metrics">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-chart-bar card-icon"></i>
                            Performance Metrics
                        </div>
                    </div>
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-value positive">+15.2%</div>
                            <div class="metric-label">Total Return</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">72%</div>
                            <div class="metric-label">Win Rate</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">1.8</div>
                            <div class="metric-label">Profit Factor</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value negative">-3.2%</div>
                            <div class="metric-label">Max Drawdown</div>
                        </div>
                    </div>
                </div>

                <!-- Recent Trades -->
                <div class="card recent-trades">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-history card-icon"></i>
                            Recent Trades
                        </div>
                    </div>
                    <div class="trades-list">
                        <div class="trade-item">
                            <div class="trade-symbol">EURUSD</div>
                            <div class="trade-type buy">BUY</div>
                            <div class="trade-size">0.1</div>
                            <div class="trade-profit positive">+$25.50</div>
                        </div>
                        <div class="trade-item">
                            <div class="trade-symbol">GBPUSD</div>
                            <div class="trade-type sell">SELL</div>
                            <div class="trade-size">0.05</div>
                            <div class="trade-profit negative">-$12.30</div>
                        </div>
                        <div class="trade-item">
                            <div class="trade-symbol">USDJPY</div>
                            <div class="trade-type buy">BUY</div>
                            <div class="trade-size">0.08</div>
                            <div class="trade-profit positive">+$18.75</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backtest Tab -->
        <div id="backtest" class="tab-content">
            <div class="dashboard-grid">
                <!-- Backtest Configuration -->
                <div class="card backtest-config">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-cog card-icon"></i>
                            Backtest Configuration
                        </div>
                    </div>
                    <div class="config-form">
                        <div class="form-group">
                            <label>Trading Pair</label>
                            <select id="backtest-symbol">
                                <option value="EURUSD">EUR/USD</option>
                                <option value="GBPUSD">GBP/USD</option>
                                <option value="USDJPY">USD/JPY</option>
                                <option value="XAUUSD">XAU/USD (Gold)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Timeframe</label>
                            <select id="backtest-timeframe">
                                <option value="M1">1 Minute</option>
                                <option value="M5">5 Minutes</option>
                                <option value="M15">15 Minutes</option>
                                <option value="H1" selected>1 Hour</option>
                                <option value="H4">4 Hours</option>
                                <option value="D1">Daily</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Period (Days)</label>
                            <input type="number" id="backtest-period" value="30" min="1" max="365">
                        </div>
                        <div class="form-group">
                            <label>Strategy</label>
                            <select id="backtest-strategy">
                                <option value="ai_signals">AI Signals</option>
                                <option value="trend_following">Trend Following</option>
                                <option value="mean_reversion">Mean Reversion</option>
                                <option value="momentum">Momentum</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Initial Balance</label>
                            <input type="number" id="backtest-balance" value="10000" min="1000" step="1000">
                        </div>
                        <button class="backtest-btn" onclick="runBacktest()">
                            <i class="fas fa-play"></i>
                            Run Backtest
                        </button>
                    </div>
                </div>

                <!-- Backtest Results -->
                <div class="card backtest-results">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-chart-line card-icon"></i>
                            Backtest Results
                        </div>
                    </div>
                    <div id="backtest-output">
                        <div class="no-results">
                            <i class="fas fa-chart-line"></i>
                            <p>Run a backtest to see results</p>
                        </div>
                    </div>
                </div>

                <!-- Strategy Performance -->
                <div class="card strategy-performance">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-trophy card-icon"></i>
                            Strategy Performance
                        </div>
                    </div>
                    <div class="performance-comparison">
                        <div class="strategy-item">
                            <div class="strategy-name">AI Signals</div>
                            <div class="strategy-return positive">+18.5%</div>
                            <div class="strategy-trades">156 trades</div>
                        </div>
                        <div class="strategy-item">
                            <div class="strategy-name">Trend Following</div>
                            <div class="strategy-return positive">+12.3%</div>
                            <div class="strategy-trades">89 trades</div>
                        </div>
                        <div class="strategy-item">
                            <div class="strategy-name">Mean Reversion</div>
                            <div class="strategy-return negative">-2.1%</div>
                            <div class="strategy-trades">203 trades</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Info Tab -->
        <div id="ai-info" class="tab-content">
            <div class="dashboard-grid">
                <!-- AI Model Overview -->
                <div class="card ai-overview">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-brain card-icon"></i>
                            AI Model Overview
                        </div>
                    </div>
                    <div class="ai-description">
                        <h4>Multi-Factor AI Trading Engine</h4>
                        <p>Our AI system combines multiple technical analysis factors to generate high-probability trading signals:</p>
                        <ul class="ai-features">
                            <li><strong>Trend Analysis:</strong> Identifies market direction using moving averages and trend lines</li>
                            <li><strong>Momentum Indicators:</strong> RSI, MACD, and Stochastic oscillators for entry timing</li>
                            <li><strong>Volume Analysis:</strong> Confirms signals with volume patterns and flow</li>
                            <li><strong>Sentiment Analysis:</strong> Market sentiment from news and social media</li>
                            <li><strong>Risk Management:</strong> Dynamic position sizing and stop-loss placement</li>
                        </ul>
                    </div>
                </div>

                <!-- Signal Generation Process -->
                <div class="card signal-process">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-cogs card-icon"></i>
                            Signal Generation Process
                        </div>
                    </div>
                    <div class="process-steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h5>Data Collection</h5>
                                <p>Real-time price data, volume, and market indicators</p>
                            </div>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h5>Technical Analysis</h5>
                                <p>Calculate RSI, MACD, moving averages, and trend strength</p>
                            </div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h5>AI Processing</h5>
                                <p>Neural network analyzes patterns and generates confidence scores</p>
                            </div>
                        </div>
                        <div class="step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h5>Signal Output</h5>
                                <p>Generate BUY/SELL signals with confidence levels and targets</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Model Performance -->
                <div class="card model-performance">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-chart-bar card-icon"></i>
                            Model Performance
                        </div>
                    </div>
                    <div class="performance-stats">
                        <div class="stat-row">
                            <span class="stat-name">Accuracy Rate:</span>
                            <span class="stat-value">78.5%</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-name">Precision:</span>
                            <span class="stat-value">82.1%</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-name">Recall:</span>
                            <span class="stat-value">74.3%</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-name">F1 Score:</span>
                            <span class="stat-value">78.0%</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-name">Sharpe Ratio:</span>
                            <span class="stat-value">1.85</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-name">Max Drawdown:</span>
                            <span class="stat-value">-8.2%</span>
                        </div>
                    </div>
                </div>

                <!-- Technical Indicators -->
                <div class="card technical-indicators">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-chart-line card-icon"></i>
                            Current Technical Indicators
                        </div>
                    </div>
                    <div class="indicators-grid">
                        <div class="indicator-item">
                            <div class="indicator-name">RSI (14)</div>
                            <div class="indicator-value">65.2</div>
                            <div class="indicator-status neutral">Neutral</div>
                        </div>
                        <div class="indicator-item">
                            <div class="indicator-name">MACD</div>
                            <div class="indicator-value">0.0023</div>
                            <div class="indicator-status bullish">Bullish</div>
                        </div>
                        <div class="indicator-item">
                            <div class="indicator-name">MA (20)</div>
                            <div class="indicator-value">1.0842</div>
                            <div class="indicator-status bullish">Above</div>
                        </div>
                        <div class="indicator-item">
                            <div class="indicator-name">Bollinger</div>
                            <div class="indicator-value">Mid</div>
                            <div class="indicator-status neutral">Neutral</div>
                        </div>
                    </div>
                </div>

                <!-- Risk Management -->
                <div class="card risk-management">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-shield-alt card-icon"></i>
                            Risk Management
                        </div>
                    </div>
                    <div class="risk-details">
                        <div class="risk-item">
                            <h5>Position Sizing</h5>
                            <p>Dynamic position sizing based on account balance and volatility. Maximum 2% risk per trade.</p>
                        </div>
                        <div class="risk-item">
                            <h5>Stop Loss</h5>
                            <p>Automatic stop-loss placement using ATR (Average True Range) for optimal risk/reward ratios.</p>
                        </div>
                        <div class="risk-item">
                            <h5>Take Profit</h5>
                            <p>Multiple take-profit levels based on support/resistance and Fibonacci retracements.</p>
                        </div>
                        <div class="risk-item">
                            <h5>Correlation Filter</h5>
                            <p>Prevents over-exposure by avoiding highly correlated currency pairs simultaneously.</p>
                        </div>
                    </div>
                </div>

                <!-- Model Updates -->
                <div class="card model-updates">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-sync-alt card-icon"></i>
                            Model Updates & Training
                        </div>
                    </div>
                    <div class="update-info">
                        <div class="update-item">
                            <div class="update-date">Last Updated:</div>
                            <div class="update-value">2024-06-28 15:30 UTC</div>
                        </div>
                        <div class="update-item">
                            <div class="update-date">Training Data:</div>
                            <div class="update-value">2 years historical data</div>
                        </div>
                        <div class="update-item">
                            <div class="update-date">Next Update:</div>
                            <div class="update-value">2024-07-05 (Weekly)</div>
                        </div>
                        <div class="update-item">
                            <div class="update-date">Model Version:</div>
                            <div class="update-value">v2.1.3</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // Remove active class from all nav tabs
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }

        async function runBacktest() {
            const symbol = document.getElementById('backtest-symbol').value;
            const timeframe = document.getElementById('backtest-timeframe').value;
            const period = document.getElementById('backtest-period').value;
            const strategy = document.getElementById('backtest-strategy').value;
            const balance = document.getElementById('backtest-balance').value;

            const outputDiv = document.getElementById('backtest-output');
            outputDiv.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Running backtest...</div>';

            try {
                const response = await fetch('/api/backtest', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: symbol,
                        timeframe: timeframe,
                        period_days: parseInt(period),
                        strategy: strategy,
                        initial_balance: parseFloat(balance)
                    })
                });

                const result = await response.json();

                if (result.success) {
                    outputDiv.innerHTML = `
                        <div class="results-grid">
                            <div class="result-item">
                                <div class="result-value ${result.total_return >= 0 ? 'positive' : 'negative'}">
                                    ${result.total_return >= 0 ? '+' : ''}${result.total_return.toFixed(2)}%
                                </div>
                                <div class="result-label">Total Return</div>
                            </div>
                            <div class="result-item">
                                <div class="result-value">${result.win_rate.toFixed(1)}%</div>
                                <div class="result-label">Win Rate</div>
                            </div>
                            <div class="result-item">
                                <div class="result-value">${result.total_trades}</div>
                                <div class="result-label">Total Trades</div>
                            </div>
                            <div class="result-item">
                                <div class="result-value negative">${result.max_drawdown.toFixed(2)}%</div>
                                <div class="result-label">Max Drawdown</div>
                            </div>
                            <div class="result-item">
                                <div class="result-value">${result.profit_factor.toFixed(2)}</div>
                                <div class="result-label">Profit Factor</div>
                            </div>
                            <div class="result-item">
                                <div class="result-value">$${result.final_balance.toFixed(2)}</div>
                                <div class="result-label">Final Balance</div>
                            </div>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `<div class="error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
    """)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
