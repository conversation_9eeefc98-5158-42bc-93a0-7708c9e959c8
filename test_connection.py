#!/usr/bin/env python3
"""
Simple database connection test
"""
import asyncio
import asyncpg
from urllib.parse import quote_plus

async def test_connection():
    """Test database connection with different formats"""
    
    # Your credentials
    user = "postgres"
    password = "Charo@2020"
    host = "db.wnphgblvibgjwvknsiwk.supabase.co"
    port = "5432"
    database = "postgres"
    
    # URL encode the password
    encoded_password = quote_plus(password)
    
    # Different connection string formats to try
    connection_strings = [
        f"postgresql://{user}:{encoded_password}@{host}:{port}/{database}",
        f"postgresql://{user}:{password}@{host}:{port}/{database}",
        f"postgresql://postgres.wnphgblvibgjwvknsiwk:{encoded_password}@aws-0-us-west-1.pooler.supabase.com:6543/postgres",
    ]
    
    for i, conn_str in enumerate(connection_strings, 1):
        print(f"\n🔗 Testing connection string {i}:")
        print(f"   {conn_str}")
        
        try:
            print("   Attempting connection...")
            conn = await asyncpg.connect(conn_str)
            print("   ✅ SUCCESS! Connected to database")
            
            # Test a simple query
            result = await conn.fetchval("SELECT 1")
            print(f"   ✅ Query test successful: {result}")
            
            await conn.close()
            print(f"   ✅ Connection closed successfully")
            
            print(f"\n🎉 WORKING CONNECTION STRING:")
            print(f"DATABASE_URL=postgresql+asyncpg://{user}:{encoded_password}@{host}:{port}/{database}")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            continue
    
    print("\n❌ All connection attempts failed.")
    print("\n🔍 Please check:")
    print("1. Is your Supabase project active (not paused)?")
    print("2. Is the connection string correct?")
    print("3. Are you connected to the internet?")
    
    return False

if __name__ == "__main__":
    print("🧪 Database Connection Test")
    print("=" * 40)
    asyncio.run(test_connection())
