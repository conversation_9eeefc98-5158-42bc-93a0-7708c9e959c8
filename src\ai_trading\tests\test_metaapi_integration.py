"""
Tests for MetaApi integration
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
import pandas as pd

from src.ai_trading.core.metaapi_client import MetaApiClient
from src.ai_trading.core.account_manager import AccountManager


@pytest.fixture
def mock_metaapi():
    """Mock MetaApi SDK"""
    with patch('src.ai_trading.core.metaapi_client.MetaApi') as mock_api, \
         patch('src.ai_trading.core.metaapi_client.CopyFactory') as mock_copy:
        
        # Mock MetaApi
        mock_api_instance = MagicMock()
        mock_api.return_value = mock_api_instance
        
        # Mock CopyFactory
        mock_copy_instance = MagicMock()
        mock_copy.return_value = mock_copy_instance
        
        # Mock account API
        mock_account_api = MagicMock()
        mock_api_instance.metatrader_account_api = mock_account_api
        
        yield {
            'api': mock_api_instance,
            'copy_factory': mock_copy_instance,
            'account_api': mock_account_api
        }


@pytest.fixture
def mock_master_account():
    """Mock master account"""
    account = MagicMock()
    account.id = "master-account-123"
    account.wait_deployed = AsyncMock()
    
    # Mock connection
    connection = MagicMock()
    connection.connect = AsyncMock()
    connection.wait_synchronized = AsyncMock()
    connection.get_account_information = AsyncMock()
    connection.create_market_order = AsyncMock()
    connection.get_candles = AsyncMock()
    connection.get_positions = AsyncMock()
    connection.close_position = AsyncMock()
    connection.close = MagicMock()
    
    account.get_rpc_connection.return_value = connection
    
    return account


@pytest.fixture
async def metaapi_client():
    """Create MetaApiClient instance"""
    return MetaApiClient()


class TestMetaApiClient:
    """Test MetaApiClient functionality"""
    
    @pytest.mark.asyncio
    async def test_initialize_success(self, metaapi_client, mock_metaapi, mock_master_account):
        """Test successful initialization"""
        # Setup mocks
        mock_metaapi['account_api'].get_accounts = AsyncMock(return_value=[mock_master_account])
        
        # Test initialization
        await metaapi_client.initialize()
        
        assert metaapi_client.is_initialized == True
        assert metaapi_client.master_account == mock_master_account
        
        # Verify calls
        mock_master_account.wait_deployed.assert_called_once()
        mock_master_account.get_rpc_connection().connect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_create_master_account(self, metaapi_client, mock_metaapi, mock_master_account):
        """Test initialization when master account needs to be created"""
        # Setup mocks - no existing accounts
        mock_metaapi['account_api'].get_accounts = AsyncMock(return_value=[])
        mock_metaapi['account_api'].create_account = AsyncMock(return_value=mock_master_account)
        
        # Test initialization
        await metaapi_client.initialize()
        
        assert metaapi_client.is_initialized == True
        mock_metaapi['account_api'].create_account.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_add_subscriber_account(self, metaapi_client, mock_metaapi, mock_master_account):
        """Test adding subscriber account"""
        # Setup
        metaapi_client.master_account = mock_master_account
        
        # Mock subscriber account
        subscriber_account = MagicMock()
        subscriber_account.id = "subscriber-123"
        subscriber_account.wait_deployed = AsyncMock()
        
        # Mock connection
        subscriber_connection = MagicMock()
        subscriber_connection.connect = AsyncMock()
        subscriber_connection.wait_synchronized = AsyncMock()
        subscriber_connection.get_account_information = AsyncMock(return_value=MagicMock(
            balance=1000, equity=1000, currency='USD'
        ))
        
        subscriber_account.get_rpc_connection.return_value = subscriber_connection
        
        mock_metaapi['account_api'].create_account = AsyncMock(return_value=subscriber_account)
        mock_metaapi['copy_factory'].configuration_api.update_subscription = AsyncMock()
        
        # Test adding subscriber
        result = await metaapi_client.add_subscriber_account(
            account_login="12345",
            account_password="password",
            account_server="ThinkMarkets-Demo",
            user_id="user-1"
        )
        
        assert result['account_id'] == "subscriber-123"
        assert result['user_id'] == "user-1"
        assert result['status'] == 'connected'
        
        # Verify account is stored
        assert "subscriber-123" in metaapi_client.connected_accounts
    
    @pytest.mark.asyncio
    async def test_execute_trade_success(self, metaapi_client, mock_master_account):
        """Test successful trade execution"""
        # Setup
        metaapi_client.master_account = mock_master_account
        
        # Mock successful trade result
        trade_result = {
            'stringCode': 'TRADE_RETCODE_DONE',
            'orderId': 'order-123',
            'positionId': 'position-123',
            'price': 1800.50
        }
        
        connection = mock_master_account.get_rpc_connection()
        connection.create_market_order = AsyncMock(return_value=trade_result)
        
        # Test trade execution
        result = await metaapi_client.execute_trade(
            symbol="XAUUSD",
            action="buy",
            volume=0.1,
            stop_loss=1790.0,
            take_profit=1810.0
        )
        
        assert result['success'] == True
        assert result['order_id'] == 'order-123'
        assert result['symbol'] == 'XAUUSD'
        assert result['action'] == 'buy'
        assert result['volume'] == 0.1
        
        # Verify trade request
        connection.create_market_order.assert_called_once()
        call_args = connection.create_market_order.call_args[0][0]
        assert call_args['symbol'] == 'XAUUSD'
        assert call_args['volume'] == 0.1
        assert call_args['stopLoss'] == 1790.0
        assert call_args['takeProfit'] == 1810.0
    
    @pytest.mark.asyncio
    async def test_execute_trade_failure(self, metaapi_client, mock_master_account):
        """Test failed trade execution"""
        # Setup
        metaapi_client.master_account = mock_master_account
        
        # Mock failed trade result
        trade_result = {
            'stringCode': 'TRADE_RETCODE_INVALID_VOLUME',
            'description': 'Invalid volume'
        }
        
        connection = mock_master_account.get_rpc_connection()
        connection.create_market_order = AsyncMock(return_value=trade_result)
        
        # Test trade execution
        result = await metaapi_client.execute_trade(
            symbol="XAUUSD",
            action="buy",
            volume=0.1
        )
        
        assert result['success'] == False
        assert result['error'] == 'Invalid volume'
        assert result['code'] == 'TRADE_RETCODE_INVALID_VOLUME'
    
    @pytest.mark.asyncio
    async def test_get_market_data(self, metaapi_client, mock_master_account):
        """Test getting market data"""
        # Setup
        metaapi_client.master_account = mock_master_account
        
        # Mock candle data
        mock_candles = [
            {
                'time': datetime.now() - timedelta(minutes=10),
                'open': 1800.0,
                'high': 1805.0,
                'low': 1795.0,
                'close': 1802.0,
                'tickVolume': 1000
            },
            {
                'time': datetime.now() - timedelta(minutes=5),
                'open': 1802.0,
                'high': 1808.0,
                'low': 1800.0,
                'close': 1806.0,
                'tickVolume': 1200
            }
        ]
        
        connection = mock_master_account.get_rpc_connection()
        connection.get_candles = AsyncMock(return_value=mock_candles)
        
        # Test getting market data
        result = await metaapi_client.get_market_data("XAUUSD", "M5", 10)
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 2
        assert 'open' in result.columns
        assert 'high' in result.columns
        assert 'low' in result.columns
        assert 'close' in result.columns
        assert 'volume' in result.columns
        assert 'timestamp' in result.columns
        
        # Verify API call
        connection.get_candles.assert_called_once_with("XAUUSD", "M5", 10)
    
    @pytest.mark.asyncio
    async def test_get_account_info(self, metaapi_client, mock_master_account):
        """Test getting account information"""
        # Setup
        metaapi_client.master_account = mock_master_account
        
        # Mock account info
        mock_account_info = MagicMock()
        mock_account_info.balance = 10000.0
        mock_account_info.equity = 10500.0
        mock_account_info.margin = 500.0
        mock_account_info.margin_free = 9500.0
        mock_account_info.margin_level = 2100.0
        mock_account_info.currency = 'USD'
        mock_account_info.leverage = 100
        mock_account_info.server = 'ThinkMarkets-Demo'
        mock_account_info.name = 'Test Account'
        
        connection = mock_master_account.get_rpc_connection()
        connection.get_account_information = AsyncMock(return_value=mock_account_info)
        
        # Test getting account info
        result = await metaapi_client.get_account_info()
        
        assert result['balance'] == 10000.0
        assert result['equity'] == 10500.0
        assert result['currency'] == 'USD'
        assert result['leverage'] == 100
    
    @pytest.mark.asyncio
    async def test_remove_subscriber_account(self, metaapi_client, mock_metaapi):
        """Test removing subscriber account"""
        # Setup - add account first
        account_id = "subscriber-123"
        mock_connection = MagicMock()
        mock_connection.close = MagicMock()
        
        metaapi_client.connected_accounts[account_id] = {
            'connection': mock_connection,
            'user_id': 'user-1'
        }
        
        # Mock account removal
        mock_account = MagicMock()
        mock_account.remove = AsyncMock()
        mock_metaapi['account_api'].get_account = AsyncMock(return_value=mock_account)
        mock_metaapi['copy_factory'].configuration_api.remove_subscription = AsyncMock()
        
        # Test removal
        result = await metaapi_client.remove_subscriber_account(account_id)
        
        assert result == True
        assert account_id not in metaapi_client.connected_accounts
        
        # Verify calls
        mock_connection.close.assert_called_once()
        mock_metaapi['copy_factory'].configuration_api.remove_subscription.assert_called_once_with(account_id)
        mock_account.remove.assert_called_once()


@pytest.mark.integration
class TestMetaApiIntegration:
    """Integration tests for MetaApi functionality"""
    
    @pytest.mark.asyncio
    async def test_full_account_lifecycle(self, mock_metaapi, mock_master_account):
        """Test complete account lifecycle"""
        client = MetaApiClient()
        
        # Setup mocks
        mock_metaapi['account_api'].get_accounts = AsyncMock(return_value=[mock_master_account])
        
        # Mock subscriber account
        subscriber_account = MagicMock()
        subscriber_account.id = "subscriber-123"
        subscriber_account.wait_deployed = AsyncMock()
        
        subscriber_connection = MagicMock()
        subscriber_connection.connect = AsyncMock()
        subscriber_connection.wait_synchronized = AsyncMock()
        subscriber_connection.get_account_information = AsyncMock(return_value=MagicMock(
            balance=5000, equity=5000, currency='USD'
        ))
        subscriber_connection.close = MagicMock()
        
        subscriber_account.get_rpc_connection.return_value = subscriber_connection
        mock_metaapi['account_api'].create_account = AsyncMock(return_value=subscriber_account)
        mock_metaapi['copy_factory'].configuration_api.update_subscription = AsyncMock()
        mock_metaapi['copy_factory'].configuration_api.remove_subscription = AsyncMock()
        
        mock_account_for_removal = MagicMock()
        mock_account_for_removal.remove = AsyncMock()
        mock_metaapi['account_api'].get_account = AsyncMock(return_value=mock_account_for_removal)
        
        try:
            # Initialize
            await client.initialize()
            assert client.is_initialized
            
            # Add subscriber account
            result = await client.add_subscriber_account(
                account_login="12345",
                account_password="password",
                account_server="ThinkMarkets-Demo",
                user_id="user-1"
            )
            assert result['account_id'] == "subscriber-123"
            assert len(client.connected_accounts) == 1
            
            # Remove subscriber account
            success = await client.remove_subscriber_account("subscriber-123")
            assert success
            assert len(client.connected_accounts) == 0
            
        finally:
            # Cleanup
            await client.shutdown()
