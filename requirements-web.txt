# Web Application Dependencies for AI Forex Trading Platform

# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.12.1

# HTTP client for MetaApi
httpx==0.25.2
aiohttp==3.9.1

# Data validation
pydantic==2.5.0
email-validator==2.1.0

# Environment and configuration
python-dotenv==1.0.0

# Logging and monitoring
structlog==23.2.0

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Optional: For enhanced development
watchfiles==0.21.0
