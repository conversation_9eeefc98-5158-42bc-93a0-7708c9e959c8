"""
AI Trading Signal Generator
Generates buy/sell signals using multiple AI models and technical analysis
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
import joblib
import json

from src.ai_trading.models.feature_extractor import FeatureExtractor
from src.ai_trading.models.volatility_filter import VolatilityFilter
from src.ai_trading.utils.logger import get_logger
from config import settings

logger = get_logger(__name__)


class SignalGenerator:
    """AI-powered trading signal generator"""
    
    def __init__(self, model_path: str = "models/signal_generator.pkl"):
        self.model_path = model_path
        self.scaler_path = model_path.replace('.pkl', '_scaler.pkl')
        self.feature_names_path = model_path.replace('.pkl', '_features.json')
        
        self.model = None
        self.scaler = None
        self.feature_names = []
        self.feature_extractor = FeatureExtractor()
        self.volatility_filter = VolatilityFilter()
        
        # Load existing model if available
        self._load_model()
    
    def _load_model(self):
        """Load trained model and scaler"""
        try:
            self.model = joblib.load(self.model_path)
            self.scaler = joblib.load(self.scaler_path)
            
            with open(self.feature_names_path, 'r') as f:
                self.feature_names = json.load(f)
            
            logger.info(f"Signal generator model loaded from {self.model_path}")
            
        except FileNotFoundError:
            logger.info("No existing signal generator model found")
        except Exception as e:
            logger.error(f"Error loading signal generator model: {e}")
    
    def generate_signal(self, ohlcv_data: pd.DataFrame, symbol: str = "XAUUSD") -> Dict[str, Any]:
        """
        Generate trading signal for given market data
        
        Args:
            ohlcv_data: OHLCV market data
            symbol: Trading symbol
            
        Returns:
            Signal dictionary with action, confidence, and metadata
        """
        try:
            # First check volatility filter
            is_safe, volatility_confidence = self.volatility_filter.predict(ohlcv_data, symbol)
            
            if not is_safe or volatility_confidence < settings.volatility_threshold:
                return {
                    'action': 'hold',
                    'confidence': 0.0,
                    'reason': 'volatility_filter_rejected',
                    'volatility_safe': False,
                    'volatility_confidence': volatility_confidence,
                    'timestamp': datetime.now()
                }
            
            # Extract features for signal generation
            features = self.feature_extractor.extract_features(ohlcv_data, symbol)
            
            if not self.model:
                # Fallback to simple technical analysis
                return self._generate_fallback_signal(features, ohlcv_data)
            
            # Prepare features for prediction
            feature_array = self._prepare_features(features)
            
            if feature_array is None:
                return self._generate_fallback_signal(features, ohlcv_data)
            
            # Generate prediction
            prediction = self.model.predict(feature_array)[0]
            probabilities = self.model.predict_proba(feature_array)[0]
            confidence = max(probabilities)
            
            # Map prediction to action
            action_map = {0: 'sell', 1: 'hold', 2: 'buy'}
            action = action_map.get(prediction, 'hold')
            
            # Calculate additional signal metadata
            signal_strength = self._calculate_signal_strength(features, confidence)
            entry_price = ohlcv_data['close'].iloc[-1]
            stop_loss, take_profit = self._calculate_levels(entry_price, action, features)
            
            return {
                'action': action,
                'confidence': float(confidence),
                'signal_strength': signal_strength,
                'entry_price': float(entry_price),
                'stop_loss': float(stop_loss) if stop_loss else None,
                'take_profit': float(take_profit) if take_profit else None,
                'volatility_safe': True,
                'volatility_confidence': volatility_confidence,
                'features_used': len(features),
                'model_prediction': int(prediction),
                'probabilities': probabilities.tolist(),
                'timestamp': datetime.now(),
                'symbol': symbol
            }
            
        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return {
                'action': 'hold',
                'confidence': 0.0,
                'reason': f'error: {str(e)}',
                'timestamp': datetime.now()
            }
    
    def _prepare_features(self, features: Dict[str, float]) -> Optional[np.ndarray]:
        """Prepare features for model prediction"""
        try:
            if not self.feature_names:
                logger.warning("No feature names available")
                return None
            
            # Create feature array in correct order
            feature_array = []
            for feature_name in self.feature_names:
                if feature_name in features:
                    feature_array.append(features[feature_name])
                else:
                    logger.warning(f"Missing feature: {feature_name}")
                    feature_array.append(0.0)  # Default value
            
            feature_array = np.array(feature_array).reshape(1, -1)
            
            # Scale features
            if self.scaler:
                feature_array = self.scaler.transform(feature_array)
            
            return feature_array
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            return None
    
    def _generate_fallback_signal(self, features: Dict[str, float], ohlcv_data: pd.DataFrame) -> Dict[str, Any]:
        """Generate signal using simple technical analysis when ML model is not available"""
        try:
            # Simple technical analysis based signal
            rsi = features.get('rsi_14', 50)
            bb_position = features.get('bb_position', 0.5)
            macd_signal = features.get('macd_signal', 0)
            
            # Simple rules
            buy_signals = 0
            sell_signals = 0
            
            # RSI signals
            if rsi < 30:
                buy_signals += 1
            elif rsi > 70:
                sell_signals += 1
            
            # Bollinger Bands signals
            if bb_position < 0.2:
                buy_signals += 1
            elif bb_position > 0.8:
                sell_signals += 1
            
            # MACD signals
            if macd_signal > 0:
                buy_signals += 1
            elif macd_signal < 0:
                sell_signals += 1
            
            # Determine action
            if buy_signals > sell_signals:
                action = 'buy'
                confidence = min(0.7, 0.4 + (buy_signals - sell_signals) * 0.1)
            elif sell_signals > buy_signals:
                action = 'sell'
                confidence = min(0.7, 0.4 + (sell_signals - buy_signals) * 0.1)
            else:
                action = 'hold'
                confidence = 0.3
            
            entry_price = ohlcv_data['close'].iloc[-1]
            stop_loss, take_profit = self._calculate_levels(entry_price, action, features)
            
            return {
                'action': action,
                'confidence': confidence,
                'signal_strength': 'medium',
                'entry_price': float(entry_price),
                'stop_loss': float(stop_loss) if stop_loss else None,
                'take_profit': float(take_profit) if take_profit else None,
                'volatility_safe': True,
                'method': 'fallback_technical_analysis',
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Error in fallback signal generation: {e}")
            return {
                'action': 'hold',
                'confidence': 0.0,
                'reason': f'fallback_error: {str(e)}',
                'timestamp': datetime.now()
            }
    
    def _calculate_signal_strength(self, features: Dict[str, float], confidence: float) -> str:
        """Calculate signal strength based on features and confidence"""
        try:
            # Combine multiple factors
            strength_score = confidence
            
            # Add volatility factor
            atr = features.get('atr_14', 0)
            if atr > 0:
                # Higher ATR might indicate stronger moves
                strength_score += min(0.2, atr / 100)
            
            # Add volume factor
            volume_ratio = features.get('volume_ratio', 1)
            if volume_ratio > 1.5:
                strength_score += 0.1
            
            # Classify strength
            if strength_score >= 0.8:
                return 'very_strong'
            elif strength_score >= 0.7:
                return 'strong'
            elif strength_score >= 0.6:
                return 'medium'
            elif strength_score >= 0.5:
                return 'weak'
            else:
                return 'very_weak'
                
        except Exception as e:
            logger.error(f"Error calculating signal strength: {e}")
            return 'unknown'
    
    def _calculate_levels(self, entry_price: float, action: str, features: Dict[str, float]) -> Tuple[Optional[float], Optional[float]]:
        """Calculate stop loss and take profit levels"""
        try:
            if action == 'hold':
                return None, None
            
            # Use ATR for dynamic levels
            atr = features.get('atr_14', entry_price * 0.01)  # Default to 1% if no ATR
            
            # Risk management parameters
            stop_loss_multiplier = 1.5  # 1.5x ATR for stop loss
            take_profit_multiplier = 2.5  # 2.5x ATR for take profit
            
            if action == 'buy':
                stop_loss = entry_price - (atr * stop_loss_multiplier)
                take_profit = entry_price + (atr * take_profit_multiplier)
            else:  # sell
                stop_loss = entry_price + (atr * stop_loss_multiplier)
                take_profit = entry_price - (atr * take_profit_multiplier)
            
            return stop_loss, take_profit
            
        except Exception as e:
            logger.error(f"Error calculating levels: {e}")
            return None, None
    
    def train(self, training_data: pd.DataFrame, model_type: str = "random_forest") -> Dict[str, float]:
        """
        Train the signal generation model
        
        Args:
            training_data: DataFrame with features and target labels
            model_type: Type of model to train
            
        Returns:
            Training metrics
        """
        try:
            logger.info(f"Training signal generator with {len(training_data)} samples")
            
            # Prepare features and target
            feature_columns = [col for col in training_data.columns if col not in ['signal', 'timestamp']]
            X = training_data[feature_columns]
            y = training_data['signal']  # 0=sell, 1=hold, 2=buy
            
            # Store feature names
            self.feature_names = feature_columns
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train model
            if model_type == "random_forest":
                self.model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42,
                    class_weight='balanced'
                )
            elif model_type == "gradient_boosting":
                self.model = GradientBoostingClassifier(
                    n_estimators=100,
                    max_depth=6,
                    random_state=42
                )
            else:
                self.model = LogisticRegression(
                    random_state=42,
                    class_weight='balanced',
                    max_iter=1000
                )
            
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            train_accuracy = self.model.score(X_train_scaled, y_train)
            test_accuracy = self.model.score(X_test_scaled, y_test)
            
            # Cross-validation
            cv_scores = cross_val_score(self.model, X_train_scaled, y_train, cv=5)
            
            # Save model
            self._save_model()
            
            metrics = {
                'train_accuracy': train_accuracy,
                'test_accuracy': test_accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'n_samples': len(training_data),
                'n_features': len(feature_columns),
                'model_type': model_type
            }
            
            logger.info(f"Signal generator training completed: {metrics}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error training signal generator: {e}")
            raise
    
    def _save_model(self):
        """Save trained model and scaler"""
        try:
            # Create models directory if it doesn't exist
            import os
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            
            # Save model and scaler
            joblib.dump(self.model, self.model_path)
            joblib.dump(self.scaler, self.scaler_path)
            
            # Save feature names
            with open(self.feature_names_path, 'w') as f:
                json.dump(self.feature_names, f)
            
            logger.info(f"Signal generator model saved to {self.model_path}")
            
        except Exception as e:
            logger.error(f"Error saving signal generator model: {e}")
            raise
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance from trained model"""
        try:
            if not self.model or not hasattr(self.model, 'feature_importances_'):
                return {}
            
            importance_dict = {}
            for i, importance in enumerate(self.model.feature_importances_):
                if i < len(self.feature_names):
                    importance_dict[self.feature_names[i]] = float(importance)
            
            # Sort by importance
            return dict(sorted(importance_dict.items(), key=lambda x: x[1], reverse=True))
            
        except Exception as e:
            logger.error(f"Error getting feature importance: {e}")
            return {}


class TrainingDataGenerator:
    """Generate training data for signal generation"""
    
    def __init__(self):
        self.feature_extractor = FeatureExtractor()
    
    def generate_sample_data(self, n_samples: int = 1000) -> pd.DataFrame:
        """Generate sample training data for signal generation"""
        try:
            logger.info(f"Generating {n_samples} training samples for signal generation")
            
            data = []
            
            for i in range(n_samples):
                try:
                    # Generate mock OHLCV data
                    ohlcv_data = self._generate_mock_ohlcv()
                    
                    # Extract features
                    features = self.feature_extractor.extract_features(ohlcv_data)
                    
                    # Generate label based on market conditions
                    signal = self._generate_signal_label(features, ohlcv_data)
                    
                    # Add to dataset
                    sample = features.copy()
                    sample['signal'] = signal
                    sample['timestamp'] = datetime.now()
                    
                    data.append(sample)
                    
                except Exception as e:
                    logger.warning(f"Failed to generate sample {i}: {e}")
                    continue
            
            df = pd.DataFrame(data)
            logger.info(f"Generated {len(df)} valid training samples")
            
            return df
            
        except Exception as e:
            logger.error(f"Error generating training data: {e}")
            return pd.DataFrame()
    
    def _generate_mock_ohlcv(self) -> pd.DataFrame:
        """Generate mock OHLCV data"""
        # Generate realistic price movement
        base_price = np.random.uniform(1700, 1900)  # Gold price range
        n_candles = 50
        
        data = []
        current_price = base_price
        
        for i in range(n_candles):
            # Random walk with trend
            trend = np.random.choice([-1, 0, 1], p=[0.3, 0.4, 0.3])
            volatility = np.random.uniform(0.5, 3.0)
            
            price_change = np.random.normal(trend * 0.5, volatility)
            current_price += price_change
            
            # Generate OHLC
            open_price = current_price
            high_price = open_price + abs(np.random.normal(0, volatility))
            low_price = open_price - abs(np.random.normal(0, volatility))
            close_price = np.random.uniform(low_price, high_price)
            
            data.append({
                'timestamp': datetime.now() - timedelta(minutes=5*(n_candles-i)),
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': np.random.uniform(1000, 5000)
            })
            
            current_price = close_price
        
        return pd.DataFrame(data)
    
    def _generate_signal_label(self, features: Dict[str, float], ohlcv_data: pd.DataFrame) -> int:
        """Generate signal label based on features and price action"""
        try:
            # Simple labeling logic based on technical indicators
            buy_score = 0
            sell_score = 0
            
            # RSI signals
            rsi = features.get('rsi_14', 50)
            if rsi < 30:
                buy_score += 2
            elif rsi > 70:
                sell_score += 2
            elif rsi < 40:
                buy_score += 1
            elif rsi > 60:
                sell_score += 1
            
            # Bollinger Bands
            bb_position = features.get('bb_position', 0.5)
            if bb_position < 0.2:
                buy_score += 1
            elif bb_position > 0.8:
                sell_score += 1
            
            # MACD
            macd_signal = features.get('macd_signal', 0)
            if macd_signal > 0:
                buy_score += 1
            elif macd_signal < 0:
                sell_score += 1
            
            # Price momentum
            price_change = (ohlcv_data['close'].iloc[-1] - ohlcv_data['close'].iloc[-5]) / ohlcv_data['close'].iloc[-5]
            if price_change > 0.01:  # 1% increase
                buy_score += 1
            elif price_change < -0.01:  # 1% decrease
                sell_score += 1
            
            # Determine signal
            if buy_score > sell_score + 1:
                return 2  # Buy
            elif sell_score > buy_score + 1:
                return 0  # Sell
            else:
                return 1  # Hold
                
        except Exception as e:
            logger.error(f"Error generating signal label: {e}")
            return 1  # Default to hold
