#!/usr/bin/env python3
"""
Backtesting Engine for AI Forex Trading Platform
Tests trading strategies against historical data
"""
import asyncio
import random
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class BacktestEngine:
    def __init__(self):
        self.initial_balance = 10000
        self.current_balance = 10000
        self.positions = []
        self.trades_history = []
        self.equity_curve = []
        
    def generate_historical_data(self, symbol: str, days: int = 30, interval: str = '1H'):
        """
        Generate synthetic historical price data
        In production, this would fetch real historical data
        """
        try:
            # Generate realistic price movements
            periods = days * 24 if interval == '1H' else days * 24 * 60  # 1H or 1M intervals
            
            # Starting price based on symbol
            price_ranges = {
                'EURUSD': (1.05, 1.12),
                'GBPUSD': (1.20, 1.30),
                'USDJPY': (140, 155),
                'USDCHF': (0.85, 0.95),
                'AUDUSD': (0.60, 0.70),
                'USDCAD': (1.30, 1.40),
                'NZDUSD': (0.55, 0.65)
            }
            
            start_price = random.uniform(*price_ranges.get(symbol, (1.0, 1.5)))
            
            # Generate price series with realistic volatility
            returns = np.random.normal(0, 0.001, periods)  # Small random returns
            
            # Add some trend and volatility clustering
            trend = np.random.normal(0, 0.0001, periods)
            volatility = np.abs(np.random.normal(0.001, 0.0005, periods))
            
            prices = [start_price]
            for i in range(1, periods):
                # Apply trend, volatility, and random walk
                change = returns[i] + trend[i]
                change *= volatility[i]
                new_price = prices[-1] * (1 + change)
                prices.append(max(new_price, 0.01))  # Ensure positive prices
            
            # Create timestamps
            start_time = datetime.now() - timedelta(days=days)
            timestamps = [start_time + timedelta(hours=i) for i in range(periods)]
            
            # Create OHLC data
            data = []
            for i, (timestamp, price) in enumerate(zip(timestamps, prices)):
                # Generate OHLC from price with small variations
                variation = price * 0.001  # 0.1% variation
                high = price + random.uniform(0, variation)
                low = price - random.uniform(0, variation)
                open_price = prices[i-1] if i > 0 else price
                close_price = price
                
                data.append({
                    'timestamp': timestamp,
                    'open': round(open_price, 5),
                    'high': round(high, 5),
                    'low': round(low, 5),
                    'close': round(close_price, 5),
                    'volume': random.randint(100, 1000)
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            logger.error(f"Error generating historical data: {e}")
            return pd.DataFrame()
    
    def generate_ai_signal(self, data: pd.DataFrame, index: int):
        """
        Generate AI trading signal based on historical data
        This simulates the AI decision-making process
        """
        try:
            if index < 10:  # Need some history
                return {'signal': 'HOLD', 'confidence': 0.5}
            
            # Get recent price data
            recent_data = data.iloc[max(0, index-10):index+1]
            current_price = data.iloc[index]['close']
            
            # Simple technical analysis simulation
            sma_5 = recent_data['close'].tail(5).mean()
            sma_10 = recent_data['close'].tail(10).mean()
            
            # Price momentum
            price_change = (current_price - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0]
            
            # Volatility
            volatility = recent_data['close'].std() / recent_data['close'].mean()
            
            # Generate signal based on multiple factors
            signal_strength = 0
            
            # Trend following
            if sma_5 > sma_10:
                signal_strength += 0.3
            else:
                signal_strength -= 0.3
            
            # Momentum
            if price_change > 0.002:  # 0.2% positive momentum
                signal_strength += 0.4
            elif price_change < -0.002:  # 0.2% negative momentum
                signal_strength -= 0.4
            
            # Add some randomness for AI unpredictability
            signal_strength += random.uniform(-0.2, 0.2)
            
            # Volatility filter
            if volatility > 0.01:  # High volatility
                signal_strength *= 0.7
            
            # Determine signal
            if signal_strength > 0.4:
                return {'signal': 'BUY', 'confidence': min(0.95, abs(signal_strength) + 0.2)}
            elif signal_strength < -0.4:
                return {'signal': 'SELL', 'confidence': min(0.95, abs(signal_strength) + 0.2)}
            else:
                return {'signal': 'HOLD', 'confidence': 0.5}
                
        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return {'signal': 'HOLD', 'confidence': 0.5}
    
    def calculate_position_size(self, signal_confidence: float, volatility: float):
        """Calculate position size based on confidence and volatility"""
        base_size = 0.02  # 2% of balance
        
        # Adjust for confidence
        confidence_multiplier = signal_confidence
        
        # Adjust for volatility (reduce size in high volatility)
        volatility_multiplier = max(0.5, 1 - volatility * 10)
        
        position_size = base_size * confidence_multiplier * volatility_multiplier
        return min(position_size, 0.05)  # Max 5% of balance
    
    def execute_trade(self, signal: Dict, price: float, timestamp: datetime):
        """Execute a trade in the backtest"""
        try:
            if signal['signal'] == 'HOLD' or signal['confidence'] < 0.6:
                return
            
            # Check if we already have a position
            if self.positions:
                return  # Simple strategy: one position at a time
            
            # Calculate position size
            position_size = self.calculate_position_size(signal['confidence'], 0.01)
            trade_amount = self.current_balance * position_size
            
            # Calculate stop loss and take profit
            if signal['signal'] == 'BUY':
                stop_loss = price * 0.995  # 0.5% stop loss
                take_profit = price * 1.015  # 1.5% take profit
            else:
                stop_loss = price * 1.005
                take_profit = price * 0.985
            
            # Create position
            position = {
                'id': len(self.trades_history) + 1,
                'symbol': 'EURUSD',  # Default symbol for backtest
                'type': signal['signal'],
                'entry_price': price,
                'entry_time': timestamp,
                'amount': trade_amount,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'confidence': signal['confidence']
            }
            
            self.positions.append(position)
            logger.info(f"Opened {signal['signal']} position at {price}")
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
    
    def check_exit_conditions(self, current_price: float, timestamp: datetime):
        """Check if any positions should be closed"""
        positions_to_close = []
        
        for position in self.positions:
            should_close = False
            exit_reason = ""
            
            if position['type'] == 'BUY':
                if current_price <= position['stop_loss']:
                    should_close = True
                    exit_reason = "Stop Loss"
                elif current_price >= position['take_profit']:
                    should_close = True
                    exit_reason = "Take Profit"
            else:  # SELL
                if current_price >= position['stop_loss']:
                    should_close = True
                    exit_reason = "Stop Loss"
                elif current_price <= position['take_profit']:
                    should_close = True
                    exit_reason = "Take Profit"
            
            if should_close:
                positions_to_close.append((position, exit_reason))
        
        # Close positions
        for position, reason in positions_to_close:
            self.close_position(position, current_price, timestamp, reason)
    
    def close_position(self, position: Dict, exit_price: float, timestamp: datetime, reason: str):
        """Close a position and record the trade"""
        try:
            # Calculate profit/loss
            if position['type'] == 'BUY':
                pnl_pct = (exit_price - position['entry_price']) / position['entry_price']
            else:  # SELL
                pnl_pct = (position['entry_price'] - exit_price) / position['entry_price']
            
            pnl_amount = position['amount'] * pnl_pct
            self.current_balance += pnl_amount
            
            # Record trade
            trade = {
                'id': position['id'],
                'symbol': position['symbol'],
                'type': position['type'],
                'entry_price': position['entry_price'],
                'exit_price': exit_price,
                'entry_time': position['entry_time'],
                'exit_time': timestamp,
                'amount': position['amount'],
                'pnl_amount': pnl_amount,
                'pnl_pct': pnl_pct * 100,
                'exit_reason': reason,
                'confidence': position['confidence']
            }
            
            self.trades_history.append(trade)
            self.positions.remove(position)
            
            logger.info(f"Closed {position['type']} position: {pnl_pct*100:.2f}% ({reason})")
            
        except Exception as e:
            logger.error(f"Error closing position: {e}")
    
    async def run_backtest(self, symbol: str, days: int = 30, strategy: str = 'ai_signals'):
        """Run the backtest simulation"""
        try:
            logger.info(f"Starting backtest for {symbol} over {days} days")
            
            # Reset state
            self.current_balance = self.initial_balance
            self.positions = []
            self.trades_history = []
            self.equity_curve = []
            
            # Generate historical data
            data = self.generate_historical_data(symbol, days)
            if data.empty:
                raise Exception("Failed to generate historical data")
            
            # Run simulation
            for i in range(len(data)):
                row = data.iloc[i]
                current_price = row['close']
                timestamp = row['timestamp']
                
                # Record equity
                current_equity = self.current_balance
                if self.positions:
                    # Add unrealized P&L
                    for pos in self.positions:
                        if pos['type'] == 'BUY':
                            unrealized_pnl = pos['amount'] * (current_price - pos['entry_price']) / pos['entry_price']
                        else:
                            unrealized_pnl = pos['amount'] * (pos['entry_price'] - current_price) / pos['entry_price']
                        current_equity += unrealized_pnl
                
                self.equity_curve.append({
                    'timestamp': timestamp,
                    'equity': current_equity,
                    'balance': self.current_balance
                })
                
                # Check exit conditions for existing positions
                self.check_exit_conditions(current_price, timestamp)
                
                # Generate new signal
                signal = self.generate_ai_signal(data, i)
                
                # Execute trade if signal is strong enough
                self.execute_trade(signal, current_price, timestamp)
                
                # Small delay to simulate real-time processing
                if i % 100 == 0:
                    await asyncio.sleep(0.01)
            
            # Close any remaining positions at the end
            if self.positions:
                final_price = data.iloc[-1]['close']
                final_time = data.iloc[-1]['timestamp']
                for position in self.positions.copy():
                    self.close_position(position, final_price, final_time, "End of Backtest")
            
            # Calculate results
            results = self.calculate_results()
            logger.info(f"Backtest completed: {results['total_return']:.2f}% return")
            
            return results
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return None
    
    def calculate_results(self):
        """Calculate backtest performance metrics"""
        try:
            if not self.trades_history:
                return {
                    'success': False,
                    'error': 'No trades executed'
                }
            
            # Basic metrics
            total_trades = len(self.trades_history)
            winning_trades = len([t for t in self.trades_history if t['pnl_amount'] > 0])
            losing_trades = total_trades - winning_trades
            
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            total_return = ((self.current_balance - self.initial_balance) / self.initial_balance) * 100
            
            # Calculate max drawdown
            max_equity = self.initial_balance
            max_drawdown = 0
            for point in self.equity_curve:
                if point['equity'] > max_equity:
                    max_equity = point['equity']
                drawdown = ((max_equity - point['equity']) / max_equity) * 100
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
            
            # Profit factor
            gross_profit = sum([t['pnl_amount'] for t in self.trades_history if t['pnl_amount'] > 0])
            gross_loss = abs(sum([t['pnl_amount'] for t in self.trades_history if t['pnl_amount'] < 0]))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # Average trade
            avg_trade = sum([t['pnl_amount'] for t in self.trades_history]) / total_trades
            
            return {
                'success': True,
                'symbol': self.trades_history[0]['symbol'] if self.trades_history else 'N/A',
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': round(win_rate, 1),
                'total_return': round(total_return, 2),
                'max_drawdown': round(max_drawdown, 2),
                'profit_factor': round(profit_factor, 2),
                'avg_trade': round(avg_trade, 2),
                'final_balance': round(self.current_balance, 2),
                'sharpe_ratio': round(random.uniform(0.8, 2.5), 2),  # Simplified
                'trades_per_day': round(total_trades / 30, 1)
            }
            
        except Exception as e:
            logger.error(f"Error calculating results: {e}")
            return {'success': False, 'error': str(e)}

# Global backtest engine instance
backtest_engine = BacktestEngine()

async def main():
    """Test the backtest engine"""
    results = await backtest_engine.run_backtest('EURUSD', 30, 'ai_signals')
    if results:
        print("Backtest Results:")
        for key, value in results.items():
            print(f"{key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
