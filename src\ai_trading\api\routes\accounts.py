"""
Account management routes
"""
from fastapi import APIRouter

router = APIRouter()


@router.post("/connect")
async def connect_mt5_account():
    """Connect MT5 account"""
    # TODO: Implement MT5 account connection
    return {"message": "Connect MT5 account - TODO"}


@router.get("/")
async def get_connected_accounts():
    """Get connected accounts"""
    # TODO: Implement get connected accounts
    return {"message": "Get connected accounts - TODO"}


@router.delete("/{account_id}")
async def disconnect_account(account_id: str):
    """Disconnect MT5 account"""
    # TODO: Implement account disconnection
    return {"message": f"Disconnect account {account_id} - TODO"}
