"""
Account management routes for MT5 account connection and management
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, validator

from src.ai_trading.api.auth import get_current_active_user
from src.ai_trading.database.models import User, MT5Account
from src.ai_trading.core.account_manager import AccountManager
from src.ai_trading.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


# Pydantic models
class MT5AccountConnection(BaseModel):
    login: str
    password: str
    server: str
    account_name: Optional[str] = None

    @validator('login')
    def validate_login(cls, v):
        if not v or len(v) < 3:
            raise ValueError('Login must be at least 3 characters')
        return v

    @validator('password')
    def validate_password(cls, v):
        if not v or len(v) < 6:
            raise ValueError('Password must be at least 6 characters')
        return v

    @validator('server')
    def validate_server(cls, v):
        if not v:
            raise ValueError('Server is required')
        return v


class MT5AccountInfo(BaseModel):
    id: int
    login: str
    server: str
    account_name: Optional[str]
    status: str
    last_balance: float
    last_equity: float
    currency: str
    leverage: int
    created_at: str

    class Config:
        from_attributes = True


class AccountStatus(BaseModel):
    account_id: int
    status: str
    balance: Optional[float] = None
    equity: Optional[float] = None
    error_message: Optional[str] = None


@router.post("/connect", response_model=dict, status_code=status.HTTP_201_CREATED)
async def connect_mt5_account(
    account_data: MT5AccountConnection,
    current_user: User = Depends(get_current_active_user)
):
    """Connect a new MT5 account"""
    try:
        logger.info(f"MT5 account connection attempt for user: {current_user.email}")

        # Initialize account manager
        account_manager = AccountManager()
        await account_manager.initialize()

        # Connect MT5 account
        mt5_account = await account_manager.connect_user_account(
            user_id=current_user.id,
            login=account_data.login,
            password=account_data.password,
            server=account_data.server,
            account_name=account_data.account_name
        )

        if not mt5_account:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to connect MT5 account"
            )

        logger.info(f"MT5 account connected successfully: {account_data.login}")

        return {
            "message": "MT5 account connected successfully",
            "account_id": mt5_account.id,
            "login": mt5_account.login,
            "server": mt5_account.server,
            "status": mt5_account.status
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MT5 account connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to connect MT5 account"
        )


@router.get("/", response_model=List[MT5AccountInfo])
async def get_connected_accounts(current_user: User = Depends(get_current_active_user)):
    """Get all connected MT5 accounts for the current user"""
    try:
        # Initialize account manager
        account_manager = AccountManager()
        await account_manager.initialize()

        # Get user's MT5 accounts
        accounts = await account_manager.db_manager.get_user_mt5_accounts(current_user.id)

        # Convert to response format
        account_list = []
        for account in accounts:
            account_list.append(MT5AccountInfo(
                id=account.id,
                login=account.login,
                server=account.server,
                account_name=account.account_name,
                status=account.status,
                last_balance=account.last_balance,
                last_equity=account.last_equity,
                currency=account.currency,
                leverage=account.leverage,
                created_at=account.created_at.isoformat()
            ))

        return account_list

    except Exception as e:
        logger.error(f"Error getting connected accounts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve connected accounts"
        )


@router.get("/{account_id}", response_model=MT5AccountInfo)
async def get_account_details(
    account_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Get details of a specific MT5 account"""
    try:
        # Initialize account manager
        account_manager = AccountManager()
        await account_manager.initialize()

        # Get account
        account = await account_manager.db_manager.get_mt5_account(account_id)

        if not account or account.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Account not found"
            )

        return MT5AccountInfo(
            id=account.id,
            login=account.login,
            server=account.server,
            account_name=account.account_name,
            status=account.status,
            last_balance=account.last_balance,
            last_equity=account.last_equity,
            currency=account.currency,
            leverage=account.leverage,
            created_at=account.created_at.isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting account details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve account details"
        )


@router.post("/{account_id}/status", response_model=AccountStatus)
async def check_account_status(
    account_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Check the status of an MT5 account"""
    try:
        # Initialize account manager
        account_manager = AccountManager()
        await account_manager.initialize()

        # Get account
        account = await account_manager.db_manager.get_mt5_account(account_id)

        if not account or account.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Account not found"
            )

        # Check account status
        status_info = await account_manager.check_account_status(account_id)

        return AccountStatus(
            account_id=account_id,
            status=status_info.get("status", "unknown"),
            balance=status_info.get("balance"),
            equity=status_info.get("equity"),
            error_message=status_info.get("error")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking account status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check account status"
        )


@router.put("/{account_id}", response_model=dict)
async def update_account(
    account_id: int,
    account_name: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """Update MT5 account information"""
    try:
        # Initialize account manager
        account_manager = AccountManager()
        await account_manager.initialize()

        # Get account
        account = await account_manager.db_manager.get_mt5_account(account_id)

        if not account or account.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Account not found"
            )

        # Update account
        await account_manager.db_manager.update_mt5_account(
            account_id=account_id,
            account_name=account_name
        )

        logger.info(f"MT5 account updated: {account_id}")

        return {"message": "Account updated successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating account: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update account"
        )


@router.delete("/{account_id}", response_model=dict)
async def disconnect_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Disconnect and remove MT5 account"""
    try:
        # Initialize account manager
        account_manager = AccountManager()
        await account_manager.initialize()

        # Get account
        account = await account_manager.db_manager.get_mt5_account(account_id)

        if not account or account.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Account not found"
            )

        # Disconnect account
        success = await account_manager.disconnect_user_account(account_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to disconnect account"
            )

        logger.info(f"MT5 account disconnected: {account_id}")

        return {"message": "Account disconnected successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disconnecting account: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disconnect account"
        )


@router.post("/{account_id}/test-connection", response_model=dict)
async def test_account_connection(
    account_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Test MT5 account connection"""
    try:
        # Initialize account manager
        account_manager = AccountManager()
        await account_manager.initialize()

        # Get account
        account = await account_manager.db_manager.get_mt5_account(account_id)

        if not account or account.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Account not found"
            )

        # Test connection
        connection_result = await account_manager.test_account_connection(account_id)

        return {
            "account_id": account_id,
            "connection_status": "success" if connection_result else "failed",
            "message": "Connection test completed"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing account connection: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test account connection"
        )
