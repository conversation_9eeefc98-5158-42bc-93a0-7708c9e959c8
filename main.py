"""
Main entry point for the AI Forex Trading Platform
"""
import asyncio
import logging
from pathlib import Path

from src.ai_trading.core.trading_engine import TradingEngine
from src.ai_trading.api.server import create_app
from config import settings
from src.ai_trading.utils.logger import setup_logging


async def main():
    """Main application entry point"""
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("Starting AI Forex Trading Platform v1.0.0")
    logger.info(f"Environment: {settings.environment}")
    
    try:
        # Initialize trading engine
        trading_engine = TradingEngine()
        await trading_engine.initialize()
        
        # Start the trading engine in background
        trading_task = asyncio.create_task(trading_engine.run())
        
        # Start the web API server
        app = create_app()
        import uvicorn
        
        server_config = uvicorn.Config(
            app=app,
            host=settings.api_host,
            port=settings.api_port,
            reload=settings.api_reload and settings.environment == "development",
            log_level=settings.log_level.lower()
        )
        
        server = uvicorn.Server(server_config)
        server_task = asyncio.create_task(server.serve())
        
        logger.info(f"API server started on {settings.api_host}:{settings.api_port}")
        logger.info("Trading engine started")
        
        # Wait for both tasks
        await asyncio.gather(trading_task, server_task)
        
    except KeyboardInterrupt:
        logger.info("Shutting down gracefully...")
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise
    finally:
        logger.info("Application shutdown complete")


if __name__ == "__main__":
    # Ensure required directories exist
    Path("logs").mkdir(exist_ok=True)
    Path("data").mkdir(exist_ok=True)
    Path("models").mkdir(exist_ok=True)
    
    # Run the application
    asyncio.run(main())
