"""
Main FastAPI application for AI Forex Trading Platform
"""
import asyncio
from contextlib import asynccontextmanager
from pathlib import Path
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from src.ai_trading.database.database import DatabaseManager
from src.ai_trading.api.routes import auth, accounts, dashboard
from src.ai_trading.utils.logger import get_logger, setup_logging
from config import settings

# Setup logging
setup_logging()
logger = get_logger(__name__)

# Global database manager instance
db_manager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global db_manager

    # Startup
    logger.info("Starting AI Forex Trading Platform...")

    try:
        # Initialize database
        db_manager = DatabaseManager()
        await db_manager.initialize()
        logger.info("Database initialized successfully")

        # Store db_manager in app state for access in routes
        app.state.db_manager = db_manager

        yield

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down AI Forex Trading Platform...")
        if db_manager:
            await db_manager.close()


# Create FastAPI app
app = FastAPI(
    title="AI Forex Trading Platform",
    description="Advanced automated trading platform powered by artificial intelligence",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(accounts.router, prefix="/api/accounts", tags=["Accounts"])
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["Dashboard"])

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Serve main pages
@app.get("/")
async def read_index():
    """Serve main dashboard page"""
    return FileResponse('static/index.html')

@app.get("/login.html")
async def read_login():
    """Serve login page"""
    return FileResponse('static/login.html')

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "AI Forex Trading Platform",
        "version": "1.0.0"
    }

@app.get("/api/health")
async def api_health_check():
    """API health check endpoint"""
    try:
        # Check database connection
        if not app.state.db_manager or not app.state.db_manager.is_initialized:
            raise HTTPException(status_code=503, detail="Database not available")

        return {
            "status": "healthy",
            "database": "connected",
            "service": "AI Forex Trading Platform API",
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")


if __name__ == "__main__":
    # Ensure required directories exist
    Path("logs").mkdir(exist_ok=True)
    Path("data").mkdir(exist_ok=True)
    Path("models").mkdir(exist_ok=True)
    Path("static").mkdir(exist_ok=True)
    Path("templates").mkdir(exist_ok=True)

    # Development server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
