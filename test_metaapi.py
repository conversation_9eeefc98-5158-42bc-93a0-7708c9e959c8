#!/usr/bin/env python3
"""
Test MetaApi connection with your credentials
"""
import asyncio
import os
from dotenv import load_dotenv
from metaapi_cloud_sdk import MetaApi

# Load environment variables
load_dotenv()

async def test_metaapi_connection():
    """Test MetaApi connection and account setup"""
    
    print("🧪 Testing MetaApi Connection")
    print("=" * 50)
    
    # Get credentials from environment
    token = os.getenv('METAAPI_TOKEN')
    account_id = os.getenv('MASTER_ACCOUNT_ID')
    password = os.getenv('MASTER_ACCOUNT_PASSWORD')
    server = os.getenv('MASTER_ACCOUNT_SERVER')
    
    print(f"📋 Configuration:")
    print(f"   Token: {token[:20]}..." if token and len(token) > 20 else f"   Token: {token}")
    print(f"   Account ID: {account_id}")
    print(f"   Server: {server}")
    print()
    
    if not all([token, account_id, password, server]):
        print("❌ Missing required credentials in .env file")
        return False
    
    try:
        # Initialize MetaApi
        print("🔌 Initializing MetaApi...")
        api = MetaApi(token)

        # If this is an account token, try to connect directly
        print("🔗 Attempting direct connection with account token...")

        # Try to get account by token (this works with account tokens)
        try:
            account = await api.metatrader_account_api.get_account_by_token()
            print(f"✅ Found account: {account.id}")
            print(f"   Login: {account.login}")
            print(f"   Server: {account.server}")
            print(f"   Name: {account.name}")
            print(f"   State: {account.state}")
        except Exception as e:
            print(f"⚠️ Could not get account info: {str(e)}")
            print("🔄 This might be an API token, trying alternative approach...")

            # Create a dummy account object for testing
            class DummyAccount:
                def __init__(self):
                    self.id = "test-account"
                    self.login = account_id
                    self.server = server

                def get_streaming_connection(self):
                    from metaapi_cloud_sdk import StreamingMetaApiConnection
                    return StreamingMetaApiConnection(api, self.id, None)

            account = DummyAccount()

        # Test connection
        print("🔗 Testing streaming connection...")
        connection = account.get_streaming_connection()

        try:
            await connection.connect()
            print("✅ Connected to MetaApi!")

            print("⏳ Waiting for synchronization...")
            await connection.wait_synchronized(timeout_in_seconds=30)

            # Get account information
            print("📊 Getting account information...")
            account_info = await connection.get_account_information()

            print(f"✅ Account information retrieved!")
            print(f"   Account Name: {account_info.get('name', 'N/A')}")
            print(f"   Balance: ${account_info.get('balance', 0):,.2f}")
            print(f"   Equity: ${account_info.get('equity', 0):,.2f}")
            print(f"   Currency: {account_info.get('currency', 'N/A')}")
            print(f"   Leverage: 1:{account_info.get('leverage', 'N/A')}")
            print(f"   Server: {account_info.get('server', 'N/A')}")

            # Test getting positions
            print("📈 Getting current positions...")
            positions = await connection.get_positions()
            print(f"   Open Positions: {len(positions)}")

            if positions:
                for pos in positions[:3]:  # Show first 3 positions
                    print(f"   - {pos.get('symbol', 'N/A')}: {pos.get('type', 'N/A')} {pos.get('volume', 0)} lots")

            # Test getting symbols (limited test)
            print("💱 Testing symbol access...")
            try:
                symbols = await connection.get_symbols()
                print(f"   Total Symbols Available: {len(symbols)}")

                major_pairs = [s for s in symbols if s.get('symbol', '') in ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF']]
                if major_pairs:
                    print("   Sample Major Pairs:")
                    for symbol in major_pairs[:3]:
                        print(f"   - {symbol.get('symbol', 'N/A')}")
            except Exception as e:
                print(f"   ⚠️ Symbol access limited: {str(e)}")

            await connection.close()

            print()
            print("🎉 MetaApi Connection Test SUCCESSFUL!")
            print("✅ Your account is ready for AI trading")

            return True

        except Exception as conn_error:
            print(f"❌ Connection failed: {str(conn_error)}")
            return False
        
    except Exception as e:
        print(f"❌ MetaApi connection failed: {str(e)}")
        print()
        print("🔍 Troubleshooting:")
        print("1. Check your MetaApi token is valid")
        print("2. Verify account credentials (ID, password, server)")
        print("3. Ensure your MT5 account is active")
        print("4. Check if ThinkMarkets-Demo server is correct")
        
        return False

if __name__ == "__main__":
    asyncio.run(test_metaapi_connection())
