#!/usr/bin/env python3
"""
Test MetaApi connection with your credentials
"""
import asyncio
import os
from dotenv import load_dotenv
from metaapi_cloud_sdk import MetaApi

# Load environment variables explicitly
load_dotenv('.env', override=True)

async def test_metaapi_connection():
    """Test MetaApi connection and account setup"""

    print("🧪 Testing MetaApi Connection")
    print("=" * 50)

    # Get credentials from environment
    token = os.getenv('METAAPI_TOKEN')
    account_id = os.getenv('MASTER_ACCOUNT_ID')
    password = os.getenv('MASTER_ACCOUNT_PASSWORD')
    server = os.getenv('MASTER_ACCOUNT_SERVER')

    print(f"📋 Configuration:")
    print(f"   Token: {token[:50]}..." if token and len(token) > 50 else f"   Token: {token}")
    print(f"   Account ID: {account_id}")
    print(f"   Server: {server}")
    print()

    if not all([token, account_id, password, server]):
        print("❌ Missing required credentials in .env file")
        return False
    
    try:
        # Initialize MetaApi
        print("🔌 Initializing MetaApi...")
        api = MetaApi(token)

        # Get all accounts
        print("🔍 Getting accounts...")
        accounts = await api.metatrader_account_api.get_accounts_with_infinite_scroll_pagination()

        print(f"✅ Found {len(accounts)} account(s)")

        # Look for existing account or create new one
        existing_account = None
        for account in accounts:
            if account.login == account_id and account.server == server:
                existing_account = account
                break

        if existing_account:
            print(f"✅ Found existing account: {existing_account.id}")
            print(f"   Login: {existing_account.login}")
            print(f"   Server: {existing_account.server}")
            print(f"   Name: {existing_account.name}")
            print(f"   State: {existing_account.state}")
            account = existing_account
        else:
            print("➕ Creating new MetaApi account...")
            account = await api.metatrader_account_api.create_account({
                'name': f'AI Trading Account {account_id}',
                'type': 'cloud',
                'login': account_id,
                'password': password,
                'server': server,
                'platform': 'mt5',
                'magic': 123456,
                'application': 'MetaApi'
            })
            print(f"✅ Created account: {account.id}")

        # Wait for account to be deployed
        print("⏳ Waiting for account deployment...")
        deployed = False
        for i in range(30):  # Wait up to 30 seconds
            if account.state == 'DEPLOYED':
                deployed = True
                break
            await asyncio.sleep(1)
            account = await api.metatrader_account_api.get_account(account.id)
            print(f"   Account state: {account.state}")

        if not deployed:
            print("⚠️ Account deployment taking longer than expected, but continuing...")

        # Test connection
        print("🔗 Testing streaming connection...")
        connection = account.get_streaming_connection()

        try:
            await connection.connect()
            print("✅ Connected to MetaApi!")

            print("⏳ Waiting for synchronization...")
            await connection.wait_synchronized()

            # Get account information
            print("📊 Getting account information...")
            account_info = await connection.get_account_information()

            print(f"✅ Account information retrieved!")
            print(f"   Account Name: {account_info.get('name', 'N/A')}")
            print(f"   Balance: ${account_info.get('balance', 0):,.2f}")
            print(f"   Equity: ${account_info.get('equity', 0):,.2f}")
            print(f"   Currency: {account_info.get('currency', 'N/A')}")
            print(f"   Leverage: 1:{account_info.get('leverage', 'N/A')}")
            print(f"   Server: {account_info.get('server', 'N/A')}")

            # Test getting positions
            print("📈 Getting current positions...")
            positions = await connection.get_positions()
            print(f"   Open Positions: {len(positions)}")

            if positions:
                for pos in positions[:3]:  # Show first 3 positions
                    print(f"   - {pos.get('symbol', 'N/A')}: {pos.get('type', 'N/A')} {pos.get('volume', 0)} lots")

            # Test getting symbols
            print("💱 Getting available symbols...")
            symbols = await connection.get_symbols()
            major_pairs = [s for s in symbols if s.get('symbol', '') in ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF']]

            print(f"   Total Symbols Available: {len(symbols)}")
            if major_pairs:
                print("   Sample Major Pairs:")
                for symbol in major_pairs[:3]:
                    print(f"   - {symbol.get('symbol', 'N/A')}")

            await connection.close()

            print()
            print("🎉 MetaApi Connection Test SUCCESSFUL!")
            print("✅ Your account is ready for AI trading")

            return True

        except Exception as conn_error:
            print(f"❌ Connection failed: {str(conn_error)}")
            return False
        
    except Exception as e:
        print(f"❌ MetaApi connection failed: {str(e)}")
        print()
        print("🔍 Troubleshooting:")
        print("1. Check your MetaApi token is valid")
        print("2. Verify account credentials (ID, password, server)")
        print("3. Ensure your MT5 account is active")
        print("4. Check if ThinkMarkets-Demo server is correct")
        
        return False

if __name__ == "__main__":
    asyncio.run(test_metaapi_connection())
