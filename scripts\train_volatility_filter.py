#!/usr/bin/env python3
"""
Script to train the volatility filter model
"""
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.ai_trading.models.model_trainer import ModelTrainer
from src.ai_trading.utils.logger import setup_logging
from config import settings

async def main():
    """Main training script"""
    # Setup logging
    setup_logging()
    
    print("🚀 AI Forex Trading Platform - Volatility Filter Training")
    print("=" * 60)
    
    trainer = ModelTrainer()
    
    try:
        print("📊 Training volatility filter model...")
        
        # Train with sample data
        metrics = await trainer.train_volatility_filter(
            use_sample_data=True,
            n_samples=2000
        )
        
        print("\n✅ Training completed successfully!")
        print(f"📈 Training Accuracy: {metrics['train_accuracy']:.3f}")
        print(f"📈 Test Accuracy: {metrics['test_accuracy']:.3f}")
        print(f"📈 CV Score: {metrics['cv_mean']:.3f} (+/- {metrics['cv_std']:.3f})")
        print(f"📊 Samples Used: {metrics['n_samples']}")
        print(f"🔧 Features Used: {metrics['n_features']}")
        
        print("\n🧪 Evaluating model performance...")
        performance = trainer.evaluate_model_performance()
        
        if performance:
            print(f"🎯 Overall Accuracy: {performance['volatility_filter_accuracy']:.3f}")
            print(f"📊 Test Samples: {performance['total_test_samples']}")
        
        print("\n🎉 Model training and evaluation complete!")
        print("💾 Model saved to: models/volatility_filter.pkl")
        print("💾 Scaler saved to: models/volatility_scaler.pkl")
        
        print("\n📋 Next Steps:")
        print("1. Test the model with: python scripts/test_volatility_filter.py")
        print("2. Integrate with trading engine")
        print("3. Monitor performance in live trading")
        
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
