"""
Tests for the volatility filter system
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from src.ai_trading.models.volatility_filter import VolatilityFilter, TrainingDataGenerator
from src.ai_trading.models.feature_extractor import FeatureExtractor


@pytest.fixture
def sample_ohlcv_data():
    """Create sample OHLCV data for testing"""
    data = []
    base_price = 1800
    
    for i in range(25):  # Enough data for feature extraction
        price = base_price + np.random.normal(0, 10)
        data.append({
            'timestamp': datetime.now() - timedelta(minutes=5*i),
            'open': price + np.random.uniform(-2, 2),
            'high': price + np.random.uniform(0, 5),
            'low': price - np.random.uniform(0, 5),
            'close': price,
            'volume': np.random.uniform(1000, 5000)
        })
    
    return pd.DataFrame(data[::-1])  # Reverse for chronological order


@pytest.fixture
def feature_extractor():
    """Create feature extractor instance"""
    return FeatureExtractor()


@pytest.fixture
def volatility_filter():
    """Create volatility filter instance"""
    return VolatilityFilter()


class TestFeatureExtractor:
    """Test the feature extraction system"""
    
    def test_extract_features(self, feature_extractor, sample_ohlcv_data):
        """Test basic feature extraction"""
        features = feature_extractor.extract_features(sample_ohlcv_data)
        
        # Check that all expected features are present
        expected_features = feature_extractor.get_feature_names()
        
        assert isinstance(features, dict)
        assert len(features) > 0
        
        # Check for key features
        assert 'atr_14' in features
        assert 'bb_width' in features
        assert 'rsi_14' in features
        assert 'hour_of_day' in features
        
        # Check that values are numeric
        for key, value in features.items():
            assert isinstance(value, (int, float, np.number))
            assert not np.isnan(value)
    
    def test_insufficient_data(self, feature_extractor):
        """Test handling of insufficient data"""
        # Create data with too few rows
        insufficient_data = pd.DataFrame({
            'open': [1800, 1805],
            'high': [1810, 1815],
            'low': [1795, 1800],
            'close': [1805, 1810],
            'volume': [1000, 1200],
            'timestamp': [datetime.now() - timedelta(minutes=5), datetime.now()]
        })
        
        with pytest.raises(ValueError, match="Insufficient data"):
            feature_extractor.extract_features(insufficient_data)
    
    def test_missing_columns(self, feature_extractor, sample_ohlcv_data):
        """Test handling of missing columns"""
        # Remove required column
        incomplete_data = sample_ohlcv_data.drop('volume', axis=1)
        
        with pytest.raises(ValueError, match="Missing required columns"):
            feature_extractor.extract_features(incomplete_data)


class TestVolatilityFilter:
    """Test the volatility filter system"""
    
    def test_predict_without_model(self, volatility_filter, sample_ohlcv_data):
        """Test prediction without trained model"""
        # Ensure no model is loaded
        volatility_filter.model = None
        
        prediction, confidence = volatility_filter.predict(sample_ohlcv_data)
        
        # Should return default safe prediction
        assert prediction == 1
        assert confidence == 0.5
    
    @patch('joblib.load')
    def test_predict_with_mock_model(self, mock_load, volatility_filter, sample_ohlcv_data):
        """Test prediction with mocked model"""
        # Create mock model
        mock_model = MagicMock()
        mock_model.predict.return_value = [1]
        mock_model.predict_proba.return_value = [[0.3, 0.7]]
        
        mock_load.return_value = mock_model
        volatility_filter.model = mock_model
        
        prediction, confidence = volatility_filter.predict(sample_ohlcv_data)
        
        assert prediction == 1
        assert confidence == 0.7
        mock_model.predict.assert_called_once()
    
    def test_is_safe_to_trade(self, volatility_filter, sample_ohlcv_data):
        """Test the is_safe_to_trade method"""
        # Mock the predict method
        volatility_filter.predict = MagicMock(return_value=(1, 0.8))
        
        # Should be safe with high confidence
        assert volatility_filter.is_safe_to_trade(sample_ohlcv_data, threshold=0.7) == True
        
        # Should be risky with low confidence
        volatility_filter.predict = MagicMock(return_value=(1, 0.5))
        assert volatility_filter.is_safe_to_trade(sample_ohlcv_data, threshold=0.7) == False
        
        # Should be risky with risky prediction
        volatility_filter.predict = MagicMock(return_value=(0, 0.9))
        assert volatility_filter.is_safe_to_trade(sample_ohlcv_data, threshold=0.7) == False


class TestTrainingDataGenerator:
    """Test the training data generator"""
    
    def test_generate_sample_data(self):
        """Test sample data generation"""
        generator = TrainingDataGenerator()
        
        # Generate small sample for testing
        data = generator.generate_sample_data(n_samples=50)
        
        assert isinstance(data, pd.DataFrame)
        assert len(data) > 0
        assert len(data) <= 50  # Some samples might fail generation
        
        # Check required columns
        assert 'is_safe' in data.columns
        
        # Check label distribution
        safe_count = (data['is_safe'] == 1).sum()
        risky_count = (data['is_safe'] == 0).sum()
        
        assert safe_count > 0 or risky_count > 0  # At least some labels
        
        # Check that labels are binary
        unique_labels = data['is_safe'].unique()
        assert all(label in [0, 1] for label in unique_labels)
    
    def test_generate_label_logic(self):
        """Test the label generation logic"""
        generator = TrainingDataGenerator()
        
        # Test high volatility scenario (should be risky)
        high_vol_features = {
            'atr_14': 100,  # High ATR
            'bb_width': 10,  # Wide Bollinger Bands
            'std_dev_pct': 5,  # High standard deviation
            'volume_ratio': 1,
            'hour_of_day': 12
        }
        
        label = generator._generate_label(high_vol_features, true_volatility=0.05)
        assert label == 0  # Should be risky
        
        # Test low volatility scenario (should be safe)
        low_vol_features = {
            'atr_14': 10,  # Low ATR
            'bb_width': 1,  # Narrow Bollinger Bands
            'std_dev_pct': 0.5,  # Low standard deviation
            'volume_ratio': 1,
            'hour_of_day': 12
        }
        
        label = generator._generate_label(low_vol_features, true_volatility=0.01)
        assert label == 1  # Should be safe


@pytest.mark.integration
class TestVolatilityFilterIntegration:
    """Integration tests for the complete volatility filter system"""
    
    def test_full_training_pipeline(self, tmp_path):
        """Test the complete training pipeline"""
        # Create volatility filter with temporary model path
        model_path = tmp_path / "test_model.pkl"
        volatility_filter = VolatilityFilter(model_path=str(model_path))
        
        # Generate training data
        generator = TrainingDataGenerator()
        training_data = generator.generate_sample_data(n_samples=100)
        
        # Train model
        metrics = volatility_filter.train(training_data)
        
        # Check that training completed
        assert isinstance(metrics, dict)
        assert 'train_accuracy' in metrics
        assert 'test_accuracy' in metrics
        assert metrics['train_accuracy'] > 0
        assert metrics['test_accuracy'] > 0
        
        # Check that model was saved
        assert model_path.exists()
        
        # Test prediction with trained model
        sample_data = pd.DataFrame({
            'open': [1800] * 25,
            'high': [1810] * 25,
            'low': [1790] * 25,
            'close': [1805] * 25,
            'volume': [2000] * 25,
            'timestamp': [datetime.now() - timedelta(minutes=5*i) for i in range(25)]
        })
        
        prediction, confidence = volatility_filter.predict(sample_data)
        
        assert prediction in [0, 1]
        assert 0 <= confidence <= 1
