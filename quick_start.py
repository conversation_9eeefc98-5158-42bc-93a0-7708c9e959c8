#!/usr/bin/env python3
"""
Quick Start Script for AI Forex Trading Platform
This script helps you get up and running quickly with Supabase
"""
import os
import sys
import asyncio
import secrets
from pathlib import Path

def print_banner():
    """Print welcome banner"""
    print("🚀 AI Forex Trading Platform - Quick Start")
    print("=" * 50)
    print("This script will help you set up your trading platform quickly!")
    print()

def check_env_file():
    """Check if .env file exists"""
    env_file = Path(".env")
    if env_file.exists():
        print("✓ Found existing .env file")
        return True
    else:
        print("✗ No .env file found")
        return False

def create_env_file():
    """Create .env file from template"""
    print("\n📝 Creating your .env file...")
    
    # Generate a secure secret key
    secret_key = secrets.token_urlsafe(32)
    
    # Get user inputs
    print("\nPlease provide the following information:")
    print("(You can get these from your Supabase project dashboard)")
    print()
    
    database_url = input("🔗 Database URL (from Supabase): ").strip()
    if not database_url:
        print("❌ Database URL is required!")
        return False
    
    # Ensure proper format
    if database_url.startswith('postgresql://'):
        database_url = database_url.replace('postgresql://', 'postgresql+asyncpg://', 1)
    elif not database_url.startswith('postgresql+asyncpg://'):
        print("⚠️  Converting to asyncpg format...")
        database_url = 'postgresql+asyncpg://' + database_url
    
    metaapi_token = input("🔑 MetaApi Token: ").strip()
    if not metaapi_token:
        print("❌ MetaApi token is required!")
        return False
    
    master_account_id = input("📊 Master Account ID (optional, press Enter to skip): ").strip()
    master_account_password = input("🔐 Master Account Password (optional, press Enter to skip): ").strip()
    
    # Create .env content
    env_content = f"""# AI Forex Trading Platform - Environment Configuration
# Generated by quick_start.py

# =============================================================================
# DATABASE CONFIGURATION (Required)
# =============================================================================
DATABASE_URL={database_url}

# =============================================================================
# SECURITY CONFIGURATION (Required)
# =============================================================================
SECRET_KEY={secret_key}

# =============================================================================
# METAAPI CONFIGURATION (Required)
# =============================================================================
METAAPI_TOKEN={metaapi_token}
MASTER_ACCOUNT_ID={master_account_id or 'your-master-account-login'}
MASTER_ACCOUNT_PASSWORD={master_account_password or 'your-master-account-password'}
MASTER_ACCOUNT_SERVER=ThinkMarkets-Demo

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# =============================================================================
# API SERVER SETTINGS
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# =============================================================================
# JWT AUTHENTICATION SETTINGS
# =============================================================================
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
DEFAULT_RISK_PERCENT=1.0
MAX_CONCURRENT_TRADES=5
VOLATILITY_THRESHOLD=0.7

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================
MODEL_UPDATE_INTERVAL_HOURS=24
FEATURE_LOOKBACK_PERIODS=20
TRAINING_DATA_DAYS=365

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_FILE_PATH=logs/trading_bot.log
"""
    
    # Write .env file
    with open(".env", "w") as f:
        f.write(env_content)
    
    print("✅ Created .env file successfully!")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        os.system("pip install -r requirements-web.txt")
        print("✅ Dependencies installed successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

async def setup_database():
    """Set up the database"""
    print("\n🗄️  Setting up database...")
    
    try:
        from setup_database import main as setup_main
        await setup_main()
        return True
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        print("Please run 'python setup_database.py' manually")
        return False

def start_server():
    """Start the development server"""
    print("\n🌐 Starting development server...")
    print("Dashboard: http://localhost:8000")
    print("API Docs: http://localhost:8000/docs")
    print("Press Ctrl+C to stop")
    print("=" * 50)
    
    try:
        os.system("python main.py")
    except KeyboardInterrupt:
        print("\n👋 Server stopped")

async def main():
    """Main quick start function"""
    print_banner()
    
    # Check if .env exists
    if not check_env_file():
        if not create_env_file():
            print("❌ Failed to create .env file. Exiting.")
            sys.exit(1)
    
    # Install dependencies
    install_deps = input("\n📦 Install dependencies? (Y/n): ").lower().strip()
    if install_deps != 'n':
        if not install_dependencies():
            print("❌ Failed to install dependencies. Please run 'pip install -r requirements-web.txt' manually")
            sys.exit(1)
    
    # Setup database
    setup_db = input("\n🗄️  Set up database? (Y/n): ").lower().strip()
    if setup_db != 'n':
        await setup_database()
    
    # Start server
    start_now = input("\n🚀 Start the server now? (Y/n): ").lower().strip()
    if start_now != 'n':
        start_server()
    else:
        print("\n✅ Setup complete!")
        print("\nTo start your server later, run:")
        print("  python main.py")
        print("\nTo access your platform:")
        print("  Dashboard: http://localhost:8000")
        print("  API Docs: http://localhost:8000/docs")

if __name__ == "__main__":
    asyncio.run(main())
