"""
Tests for the Trading Engine
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
import pandas as pd

from src.ai_trading.core.trading_engine import TradingEngine


@pytest.fixture
def mock_components():
    """Mock all trading engine components"""
    with patch('src.ai_trading.core.trading_engine.MetaApiClient') as mock_metaapi, \
         patch('src.ai_trading.core.trading_engine.AccountManager') as mock_account_mgr, \
         patch('src.ai_trading.core.trading_engine.VolatilityFilter') as mock_vol_filter, \
         patch('src.ai_trading.core.trading_engine.SignalGenerator') as mock_signal_gen, \
         patch('src.ai_trading.core.trading_engine.DatabaseManager') as mock_db:
        
        # Mock MetaApiClient
        mock_metaapi_instance = MagicMock()
        mock_metaapi_instance.get_market_data = AsyncMock()
        mock_metaapi_instance.execute_trade = AsyncMock()
        mock_metaapi_instance.get_account_info = AsyncMock()
        mock_metaapi_instance.get_positions = AsyncMock()
        mock_metaapi.return_value = mock_metaapi_instance
        
        # Mock AccountManager
        mock_account_mgr_instance = MagicMock()
        mock_account_mgr_instance.initialize = AsyncMock()
        mock_account_mgr_instance.shutdown = AsyncMock()
        mock_account_mgr.return_value = mock_account_mgr_instance
        
        # Mock VolatilityFilter
        mock_vol_filter_instance = MagicMock()
        mock_vol_filter.return_value = mock_vol_filter_instance
        
        # Mock SignalGenerator
        mock_signal_gen_instance = MagicMock()
        mock_signal_gen_instance.generate_signal = MagicMock()
        mock_signal_gen.return_value = mock_signal_gen_instance
        
        # Mock DatabaseManager
        mock_db_instance = MagicMock()
        mock_db_instance.initialize = AsyncMock()
        mock_db_instance.close = AsyncMock()
        mock_db_instance.log_system_event = AsyncMock()
        mock_db_instance.create_trade = AsyncMock()
        mock_db_instance.get_open_trades = AsyncMock(return_value=[])
        mock_db_instance.update_trade_status = AsyncMock()
        mock_db.return_value = mock_db_instance
        
        yield {
            'metaapi': mock_metaapi_instance,
            'account_manager': mock_account_mgr_instance,
            'volatility_filter': mock_vol_filter_instance,
            'signal_generator': mock_signal_gen_instance,
            'database': mock_db_instance
        }


@pytest.fixture
def sample_market_data():
    """Sample market data for testing"""
    dates = pd.date_range(start='2023-01-01', periods=100, freq='1H')
    data = {
        'timestamp': dates,
        'open': [1800 + i * 0.1 for i in range(100)],
        'high': [1801 + i * 0.1 for i in range(100)],
        'low': [1799 + i * 0.1 for i in range(100)],
        'close': [1800.5 + i * 0.1 for i in range(100)],
        'volume': [1000 + i * 10 for i in range(100)]
    }
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    return df


@pytest.fixture
async def trading_engine(mock_components):
    """Create trading engine instance"""
    engine = TradingEngine()
    await engine.initialize()
    return engine


class TestTradingEngine:
    """Test TradingEngine functionality"""
    
    @pytest.mark.asyncio
    async def test_initialization(self, mock_components):
        """Test trading engine initialization"""
        engine = TradingEngine()
        
        await engine.initialize()
        
        # Verify components were initialized
        mock_components['database'].initialize.assert_called_once()
        mock_components['account_manager'].initialize.assert_called_once()
        
        assert engine.total_signals == 0
        assert engine.executed_trades == 0
        assert engine.successful_trades == 0
        assert len(engine.active_positions) == 0
    
    @pytest.mark.asyncio
    async def test_process_symbol_with_valid_signal(self, trading_engine, mock_components, sample_market_data):
        """Test processing symbol with valid trading signal"""
        # Setup mocks
        mock_components['metaapi'].get_market_data.return_value = sample_market_data
        
        # Mock signal generation
        mock_signal = {
            'action': 'buy',
            'confidence': 0.8,
            'entry_price': 1800.5,
            'stop_loss': 1790.0,
            'take_profit': 1820.0,
            'timestamp': datetime.now(),
            'volatility_safe': True
        }
        mock_components['signal_generator'].generate_signal.return_value = mock_signal
        
        # Mock successful trade execution
        mock_trade_result = {
            'success': True,
            'order_id': 'order-123',
            'position_id': 'pos-123',
            'price': 1800.5
        }
        mock_components['metaapi'].execute_trade.return_value = mock_trade_result
        mock_components['metaapi'].get_account_info.return_value = {'balance': 10000}
        
        # Process symbol
        await trading_engine._process_symbol('XAUUSD')
        
        # Verify signal was generated
        mock_components['signal_generator'].generate_signal.assert_called_once()
        assert trading_engine.total_signals == 1
        
        # Verify trade was executed
        mock_components['metaapi'].execute_trade.assert_called_once()
        assert trading_engine.executed_trades == 1
        
        # Verify position tracking
        assert 'XAUUSD' in trading_engine.active_positions
        position = trading_engine.active_positions['XAUUSD']
        assert position['action'] == 'buy'
        assert position['order_id'] == 'order-123'
    
    @pytest.mark.asyncio
    async def test_process_symbol_low_confidence_signal(self, trading_engine, mock_components, sample_market_data):
        """Test processing symbol with low confidence signal"""
        # Setup mocks
        mock_components['metaapi'].get_market_data.return_value = sample_market_data
        
        # Mock low confidence signal
        mock_signal = {
            'action': 'buy',
            'confidence': 0.3,  # Below threshold
            'entry_price': 1800.5,
            'timestamp': datetime.now(),
            'volatility_safe': True
        }
        mock_components['signal_generator'].generate_signal.return_value = mock_signal
        
        # Process symbol
        await trading_engine._process_symbol('XAUUSD')
        
        # Verify signal was generated but trade was not executed
        mock_components['signal_generator'].generate_signal.assert_called_once()
        assert trading_engine.total_signals == 1
        
        # Verify no trade was executed
        mock_components['metaapi'].execute_trade.assert_not_called()
        assert trading_engine.executed_trades == 0
    
    @pytest.mark.asyncio
    async def test_process_symbol_hold_signal(self, trading_engine, mock_components, sample_market_data):
        """Test processing symbol with hold signal"""
        # Setup mocks
        mock_components['metaapi'].get_market_data.return_value = sample_market_data
        
        # Mock hold signal
        mock_signal = {
            'action': 'hold',
            'confidence': 0.8,
            'timestamp': datetime.now(),
            'volatility_safe': True
        }
        mock_components['signal_generator'].generate_signal.return_value = mock_signal
        
        # Process symbol
        await trading_engine._process_symbol('XAUUSD')
        
        # Verify signal was generated but trade was not executed
        mock_components['signal_generator'].generate_signal.assert_called_once()
        assert trading_engine.total_signals == 1
        
        # Verify no trade was executed
        mock_components['metaapi'].execute_trade.assert_not_called()
        assert trading_engine.executed_trades == 0
    
    @pytest.mark.asyncio
    async def test_execute_signal_success(self, trading_engine, mock_components, sample_market_data):
        """Test successful signal execution"""
        # Setup signal
        signal = {
            'action': 'buy',
            'confidence': 0.8,
            'entry_price': 1800.5,
            'stop_loss': 1790.0,
            'take_profit': 1820.0,
            'timestamp': datetime.now()
        }
        
        # Mock successful trade execution
        mock_trade_result = {
            'success': True,
            'order_id': 'order-123',
            'position_id': 'pos-123',
            'price': 1800.5
        }
        mock_components['metaapi'].execute_trade.return_value = mock_trade_result
        mock_components['metaapi'].get_account_info.return_value = {'balance': 10000}
        
        # Execute signal
        await trading_engine._execute_signal(signal, 'XAUUSD', sample_market_data)
        
        # Verify trade execution
        mock_components['metaapi'].execute_trade.assert_called_once()
        
        # Verify database logging
        mock_components['database'].create_trade.assert_called_once()
        mock_components['database'].log_system_event.assert_called()
        
        # Verify position tracking
        assert 'XAUUSD' in trading_engine.active_positions
        assert trading_engine.executed_trades == 1
    
    @pytest.mark.asyncio
    async def test_execute_signal_failure(self, trading_engine, mock_components, sample_market_data):
        """Test failed signal execution"""
        # Setup signal
        signal = {
            'action': 'buy',
            'confidence': 0.8,
            'entry_price': 1800.5,
            'timestamp': datetime.now()
        }
        
        # Mock failed trade execution
        mock_trade_result = {
            'success': False,
            'error': 'Insufficient margin'
        }
        mock_components['metaapi'].execute_trade.return_value = mock_trade_result
        mock_components['metaapi'].get_account_info.return_value = {'balance': 10000}
        
        # Execute signal
        await trading_engine._execute_signal(signal, 'XAUUSD', sample_market_data)
        
        # Verify trade execution was attempted
        mock_components['metaapi'].execute_trade.assert_called_once()
        
        # Verify error logging
        mock_components['database'].log_system_event.assert_called()
        
        # Verify no position was created
        assert 'XAUUSD' not in trading_engine.active_positions
        assert trading_engine.executed_trades == 0
    
    @pytest.mark.asyncio
    async def test_calculate_position_size(self, trading_engine, mock_components):
        """Test position size calculation"""
        # Mock account info
        mock_components['metaapi'].get_account_info.return_value = {
            'balance': 10000
        }
        
        # Test signal with stop loss
        signal = {
            'entry_price': 1800.0,
            'stop_loss': 1790.0
        }
        
        volume = await trading_engine._calculate_position_size(signal, 'XAUUSD')
        
        # Verify position size calculation
        assert volume > 0
        assert volume <= 1.0  # Should be reasonable size
        
        # Test signal without stop loss (should use default)
        signal_no_sl = {
            'entry_price': 1800.0
        }
        
        volume_default = await trading_engine._calculate_position_size(signal_no_sl, 'XAUUSD')
        assert volume_default > 0
    
    @pytest.mark.asyncio
    async def test_update_positions(self, trading_engine, mock_components):
        """Test position updates and closed position handling"""
        # Setup active position
        trading_engine.active_positions['XAUUSD'] = {
            'action': 'buy',
            'volume': 0.1,
            'entry_price': 1800.0,
            'position_id': 'pos-123',
            'trade_id': 1
        }
        
        # Mock no current positions (position was closed)
        mock_components['metaapi'].get_positions.return_value = []
        
        # Update positions
        await trading_engine._update_positions()
        
        # Verify position was removed
        assert 'XAUUSD' not in trading_engine.active_positions
        
        # Verify database update
        mock_components['database'].update_trade_status.assert_called_once_with(1, 'closed')
        
        # Verify statistics update
        assert trading_engine.successful_trades == 1
    
    @pytest.mark.asyncio
    async def test_is_signal_actionable(self, trading_engine):
        """Test signal actionability checks"""
        # Test high confidence signal
        signal_good = {
            'action': 'buy',
            'confidence': 0.8,
            'timestamp': datetime.now()
        }
        assert trading_engine._is_signal_actionable(signal_good, 'XAUUSD') == True
        
        # Test low confidence signal
        signal_low_conf = {
            'action': 'buy',
            'confidence': 0.3,
            'timestamp': datetime.now()
        }
        assert trading_engine._is_signal_actionable(signal_low_conf, 'XAUUSD') == False
        
        # Test hold signal
        signal_hold = {
            'action': 'hold',
            'confidence': 0.8,
            'timestamp': datetime.now()
        }
        assert trading_engine._is_signal_actionable(signal_hold, 'XAUUSD') == False
        
        # Test duplicate position
        trading_engine.active_positions['XAUUSD'] = {'action': 'buy'}
        signal_duplicate = {
            'action': 'buy',
            'confidence': 0.8,
            'timestamp': datetime.now()
        }
        assert trading_engine._is_signal_actionable(signal_duplicate, 'XAUUSD') == False
    
    @pytest.mark.asyncio
    async def test_get_performance_stats(self, trading_engine):
        """Test performance statistics"""
        # Setup some test data
        trading_engine.total_signals = 100
        trading_engine.executed_trades = 50
        trading_engine.successful_trades = 30
        trading_engine.active_positions = {'XAUUSD': {}, 'EURUSD': {}}
        trading_engine.is_running = True
        
        stats = await trading_engine.get_performance_stats()
        
        assert stats['total_signals'] == 100
        assert stats['executed_trades'] == 50
        assert stats['successful_trades'] == 30
        assert stats['active_positions'] == 2
        assert stats['success_rate'] == 60.0
        assert 'XAUUSD' in stats['symbols_traded']
        assert 'EURUSD' in stats['symbols_traded']
        assert stats['uptime'] is not None
    
    @pytest.mark.asyncio
    async def test_trading_cycle(self, trading_engine, mock_components, sample_market_data):
        """Test complete trading cycle"""
        # Setup mocks
        mock_components['metaapi'].get_market_data.return_value = sample_market_data
        mock_components['metaapi'].get_positions.return_value = []
        
        # Mock signal generation
        mock_signal = {
            'action': 'buy',
            'confidence': 0.8,
            'entry_price': 1800.5,
            'timestamp': datetime.now(),
            'volatility_safe': True
        }
        mock_components['signal_generator'].generate_signal.return_value = mock_signal
        
        # Mock successful trade execution
        mock_trade_result = {
            'success': True,
            'order_id': 'order-123',
            'position_id': 'pos-123',
            'price': 1800.5
        }
        mock_components['metaapi'].execute_trade.return_value = mock_trade_result
        mock_components['metaapi'].get_account_info.return_value = {'balance': 10000}
        
        # Run trading cycle
        await trading_engine._trading_cycle()
        
        # Verify all symbols were processed
        assert mock_components['metaapi'].get_market_data.call_count == len(trading_engine.trading_symbols)
        
        # Verify positions were updated
        mock_components['metaapi'].get_positions.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_shutdown(self, trading_engine, mock_components):
        """Test trading engine shutdown"""
        trading_engine.is_running = True
        
        await trading_engine.shutdown()
        
        assert trading_engine.is_running == False
        mock_components['account_manager'].shutdown.assert_called_once()
        mock_components['database'].close.assert_called_once()


@pytest.mark.integration
class TestTradingEngineIntegration:
    """Integration tests for trading engine"""
    
    @pytest.mark.asyncio
    async def test_full_trading_workflow(self, mock_components, sample_market_data):
        """Test complete trading workflow"""
        engine = TradingEngine()
        
        # Setup comprehensive mocks
        mock_components['metaapi'].get_market_data.return_value = sample_market_data
        mock_components['metaapi'].get_positions.return_value = []
        mock_components['metaapi'].get_account_info.return_value = {'balance': 10000}
        
        # Mock signal that should trigger trade
        mock_signal = {
            'action': 'buy',
            'confidence': 0.8,
            'entry_price': 1800.5,
            'stop_loss': 1790.0,
            'take_profit': 1820.0,
            'timestamp': datetime.now(),
            'volatility_safe': True
        }
        mock_components['signal_generator'].generate_signal.return_value = mock_signal
        
        # Mock successful trade execution
        mock_trade_result = {
            'success': True,
            'order_id': 'order-123',
            'position_id': 'pos-123',
            'price': 1800.5
        }
        mock_components['metaapi'].execute_trade.return_value = mock_trade_result
        
        try:
            # Initialize and run one cycle
            await engine.initialize()
            await engine._trading_cycle()
            
            # Verify workflow completion
            assert engine.total_signals > 0
            assert engine.executed_trades > 0
            assert len(engine.active_positions) > 0
            
            # Verify database interactions
            mock_components['database'].create_trade.assert_called()
            mock_components['database'].log_system_event.assert_called()
            
        finally:
            await engine.shutdown()
