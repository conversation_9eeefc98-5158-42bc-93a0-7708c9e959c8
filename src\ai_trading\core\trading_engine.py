"""
Main trading engine for the AI Forex Trading Platform
"""
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd

from src.ai_trading.core.metaapi_client import MetaApiClient
from src.ai_trading.core.account_manager import AccountManager
from src.ai_trading.models.volatility_filter import VolatilityFilter
from src.ai_trading.models.signal_generator import SignalGenerator
from src.ai_trading.database.database import DatabaseManager
from src.ai_trading.utils.logger import get_logger
from config import settings, TradingConfig

logger = get_logger(__name__)


class TradingEngine:
    """Main AI trading engine"""

    def __init__(self):
        self.metaapi_client = MetaApiClient()
        self.account_manager = AccountManager()
        self.volatility_filter = VolatilityFilter()
        self.signal_generator = SignalGenerator()
        self.db_manager = DatabaseManager()

        self.is_running = False
        self.trading_symbols = TradingConfig.SYMBOLS
        self.trading_timeframe = TradingConfig.TIMEFRAME
        self.last_signals = {}  # Store last signals to avoid duplicate trades
        self.active_positions = {}  # Track active positions

        # Performance tracking
        self.total_signals = 0
        self.executed_trades = 0
        self.successful_trades = 0

    async def initialize(self):
        """Initialize the trading engine"""
        try:
            logger.info("Initializing Trading Engine...")

            # Initialize components
            await self.db_manager.initialize()
            await self.account_manager.initialize()

            # Load active positions
            await self._load_active_positions()

            logger.info("Trading Engine initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Trading Engine: {e}")
            raise

    async def start_trading(self):
        """Start the main trading loop"""
        try:
            logger.info("🚀 Starting AI Trading Engine...")
            logger.info(f"Trading symbols: {self.trading_symbols}")
            logger.info(f"Trading timeframe: {self.trading_timeframe}")
            logger.info(f"Trading interval: {TradingConfig.TRADING_INTERVAL} seconds")

            self.is_running = True

            while self.is_running:
                try:
                    # Main trading logic
                    await self._trading_cycle()

                    # Wait before next cycle
                    await asyncio.sleep(TradingConfig.TRADING_INTERVAL)

                except Exception as e:
                    logger.error(f"Error in trading cycle: {e}")
                    await asyncio.sleep(60)  # Wait 1 minute on error

        except Exception as e:
            logger.error(f"Trading engine error: {e}")
        finally:
            logger.info("Trading engine stopped")

    async def _trading_cycle(self):
        """Execute one trading cycle"""
        try:
            logger.debug("🔄 Executing trading cycle...")

            # Process each trading symbol
            for symbol in self.trading_symbols:
                try:
                    await self._process_symbol(symbol)
                except Exception as e:
                    logger.error(f"Error processing symbol {symbol}: {e}")

            # Update position tracking
            await self._update_positions()

            # Log cycle completion
            logger.debug(f"Trading cycle completed. Signals: {self.total_signals}, Trades: {self.executed_trades}")

        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")

    async def _process_symbol(self, symbol: str):
        """Process trading signals for a specific symbol"""
        try:
            # Get market data
            market_data = await self.metaapi_client.get_market_data(
                symbol=symbol,
                timeframe=self.trading_timeframe,
                count=100
            )

            if market_data.empty:
                logger.warning(f"No market data available for {symbol}")
                return

            # Generate trading signal
            signal = self.signal_generator.generate_signal(market_data, symbol)
            self.total_signals += 1

            logger.debug(f"Signal for {symbol}: {signal['action']} (confidence: {signal['confidence']:.3f})")

            # Check if signal is actionable
            if not self._is_signal_actionable(signal, symbol):
                return

            # Execute trade based on signal
            if signal['action'] in ['buy', 'sell']:
                await self._execute_signal(signal, symbol, market_data)

        except Exception as e:
            logger.error(f"Error processing symbol {symbol}: {e}")

    def _is_signal_actionable(self, signal: Dict[str, Any], symbol: str) -> bool:
        """Check if a signal should be acted upon"""
        try:
            # Check confidence threshold
            if signal['confidence'] < TradingConfig.MIN_SIGNAL_CONFIDENCE:
                logger.debug(f"Signal confidence too low: {signal['confidence']:.3f}")
                return False

            # Check if action is hold
            if signal['action'] == 'hold':
                return False

            # Check if we already have a position in this symbol
            if symbol in self.active_positions:
                current_position = self.active_positions[symbol]
                if current_position['action'] == signal['action']:
                    logger.debug(f"Already have {signal['action']} position for {symbol}")
                    return False

            # Check if signal is too similar to last signal (avoid overtrading)
            if symbol in self.last_signals:
                last_signal = self.last_signals[symbol]
                time_diff = (signal['timestamp'] - last_signal['timestamp']).total_seconds()

                if (time_diff < TradingConfig.MIN_SIGNAL_INTERVAL and
                    last_signal['action'] == signal['action']):
                    logger.debug(f"Signal too similar to recent signal for {symbol}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error checking signal actionability: {e}")
            return False

    async def _execute_signal(self, signal: Dict[str, Any], symbol: str, market_data: pd.DataFrame):
        """Execute a trading signal"""
        try:
            logger.info(f"🎯 Executing {signal['action'].upper()} signal for {symbol}")
            logger.info(f"   Confidence: {signal['confidence']:.3f}")
            logger.info(f"   Entry Price: {signal.get('entry_price', 'N/A')}")

            # Calculate position size
            volume = await self._calculate_position_size(signal, symbol)

            if volume <= 0:
                logger.warning(f"Invalid position size calculated: {volume}")
                return

            # Execute trade on master account
            trade_result = await self.metaapi_client.execute_trade(
                symbol=symbol,
                action=signal['action'],
                volume=volume,
                stop_loss=signal.get('stop_loss'),
                take_profit=signal.get('take_profit'),
                comment=f"AI Signal - Conf: {signal['confidence']:.3f}"
            )

            if trade_result['success']:
                logger.info(f"✅ Trade executed successfully for {symbol}")
                logger.info(f"   Order ID: {trade_result.get('order_id')}")
                logger.info(f"   Volume: {volume}")

                # Store trade in database
                await self._store_trade_record(signal, symbol, trade_result, volume)

                # Update tracking
                self.executed_trades += 1
                self.last_signals[symbol] = signal

                # Track active position
                self.active_positions[symbol] = {
                    'action': signal['action'],
                    'volume': volume,
                    'entry_price': trade_result.get('price', signal.get('entry_price')),
                    'stop_loss': signal.get('stop_loss'),
                    'take_profit': signal.get('take_profit'),
                    'timestamp': datetime.now(),
                    'order_id': trade_result.get('order_id'),
                    'position_id': trade_result.get('position_id')
                }

                # Log to database
                await self.db_manager.log_system_event(
                    level='INFO',
                    message=f"Trade executed: {signal['action']} {volume} {symbol}",
                    module='trading_engine',
                    function='_execute_signal'
                )

            else:
                logger.error(f"❌ Trade execution failed for {symbol}: {trade_result.get('error')}")

                # Log error to database
                await self.db_manager.log_system_event(
                    level='ERROR',
                    message=f"Trade execution failed: {trade_result.get('error')}",
                    module='trading_engine',
                    function='_execute_signal'
                )

        except Exception as e:
            logger.error(f"Error executing signal for {symbol}: {e}")

    async def _calculate_position_size(self, signal: Dict[str, Any], symbol: str) -> float:
        """Calculate position size based on risk management"""
        try:
            # Get account information
            account_info = await self.metaapi_client.get_account_info()

            if not account_info:
                logger.warning("Could not get account info for position sizing")
                return TradingConfig.DEFAULT_VOLUME

            balance = account_info.get('balance', 10000)

            # Risk-based position sizing
            risk_amount = balance * (TradingConfig.RISK_PERCENT / 100)

            # Calculate position size based on stop loss distance
            entry_price = signal.get('entry_price', 1800)  # Default gold price
            stop_loss = signal.get('stop_loss')

            if stop_loss:
                stop_distance = abs(entry_price - stop_loss)
                if stop_distance > 0:
                    # Position size = Risk Amount / Stop Distance
                    volume = risk_amount / stop_distance

                    # Apply volume limits
                    volume = max(TradingConfig.MIN_VOLUME, volume)
                    volume = min(TradingConfig.MAX_VOLUME, volume)

                    # Round to appropriate precision
                    volume = round(volume, 2)

                    return volume

            # Fallback to default volume
            return TradingConfig.DEFAULT_VOLUME

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return TradingConfig.DEFAULT_VOLUME

    async def _store_trade_record(self, signal: Dict[str, Any], symbol: str, trade_result: Dict[str, Any], volume: float):
        """Store trade record in database"""
        try:
            await self.db_manager.create_trade(
                user_id=1,  # Master account user ID
                symbol=symbol,
                action=signal['action'],
                volume=volume,
                entry_price=trade_result.get('price', signal.get('entry_price')),
                stop_loss=signal.get('stop_loss'),
                take_profit=signal.get('take_profit'),
                status='open',
                order_id=trade_result.get('order_id'),
                position_id=trade_result.get('position_id'),
                confidence=signal['confidence'],
                signal_data=signal
            )

        except Exception as e:
            logger.error(f"Error storing trade record: {e}")

    async def _load_active_positions(self):
        """Load active positions from database"""
        try:
            # Get open trades from database
            open_trades = await self.db_manager.get_open_trades()

            for trade in open_trades:
                symbol = trade['symbol']
                self.active_positions[symbol] = {
                    'action': trade['action'],
                    'volume': trade['volume'],
                    'entry_price': trade['entry_price'],
                    'stop_loss': trade['stop_loss'],
                    'take_profit': trade['take_profit'],
                    'timestamp': trade['created_at'],
                    'order_id': trade['order_id'],
                    'position_id': trade['position_id'],
                    'trade_id': trade['id']
                }

            logger.info(f"Loaded {len(self.active_positions)} active positions")

        except Exception as e:
            logger.error(f"Error loading active positions: {e}")

    async def _update_positions(self):
        """Update position tracking and check for closed positions"""
        try:
            # Get current positions from MetaApi
            current_positions = await self.metaapi_client.get_positions()

            # Check for closed positions
            closed_positions = []
            for symbol, position in self.active_positions.items():
                position_id = position.get('position_id')

                # Check if position still exists
                position_exists = any(
                    pos.get('id') == position_id for pos in current_positions
                )

                if not position_exists:
                    closed_positions.append(symbol)

            # Handle closed positions
            for symbol in closed_positions:
                await self._handle_closed_position(symbol)

        except Exception as e:
            logger.error(f"Error updating positions: {e}")

    async def _handle_closed_position(self, symbol: str):
        """Handle a closed position"""
        try:
            position = self.active_positions.get(symbol)
            if not position:
                return

            logger.info(f"📊 Position closed for {symbol}")

            # Update trade record in database
            trade_id = position.get('trade_id')
            if trade_id:
                await self.db_manager.update_trade_status(trade_id, 'closed')

            # Remove from active positions
            del self.active_positions[symbol]

            # Update statistics
            self.successful_trades += 1

            # Log event
            await self.db_manager.log_system_event(
                level='INFO',
                message=f"Position closed: {symbol}",
                module='trading_engine',
                function='_handle_closed_position'
            )

        except Exception as e:
            logger.error(f"Error handling closed position for {symbol}: {e}")

    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get trading performance statistics"""
        try:
            return {
                'total_signals': self.total_signals,
                'executed_trades': self.executed_trades,
                'successful_trades': self.successful_trades,
                'active_positions': len(self.active_positions),
                'success_rate': (self.successful_trades / max(1, self.executed_trades)) * 100,
                'symbols_traded': list(self.active_positions.keys()),
                'uptime': datetime.now() if self.is_running else None
            }

        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
            return {}

    async def stop_trading(self):
        """Stop the trading engine"""
        logger.info("Stopping Trading Engine...")
        self.is_running = False

    async def shutdown(self):
        """Shutdown the trading engine"""
        try:
            await self.stop_trading()
            await self.account_manager.shutdown()
            await self.db_manager.close()
            logger.info("Trading Engine shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        
        # TODO: Close all positions
        # TODO: Disconnect from MetaApi
        # TODO: Save state to database
        
        logger.info("Trading engine shutdown complete")
