"""
Account Manager for handling user MT5 accounts and copy trading setup
"""
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import hashlib
import json

from src.ai_trading.core.metaapi_client import MetaApiClient
from src.ai_trading.database.models import User, MT5Account, Trade
from src.ai_trading.database.database import DatabaseManager
from src.ai_trading.utils.logger import get_logger
from config import settings

logger = get_logger(__name__)


class AccountManager:
    """Manages user accounts and MT5 connections"""
    
    def __init__(self):
        self.metaapi_client = MetaApiClient()
        self.db_manager = DatabaseManager()
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize the account manager"""
        try:
            logger.info("Initializing Account Manager...")
            
            # Initialize database
            await self.db_manager.initialize()
            
            # Initialize MetaApi client
            await self.metaapi_client.initialize()
            
            # Load existing accounts from database
            await self._load_existing_accounts()
            
            self.is_initialized = True
            logger.info("Account Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Account Manager: {e}")
            raise
    
    async def _load_existing_accounts(self):
        """Load existing MT5 accounts from database and reconnect"""
        try:
            logger.info("Loading existing MT5 accounts...")
            
            # Get all active MT5 accounts from database
            accounts = await self.db_manager.get_active_mt5_accounts()
            
            for account in accounts:
                try:
                    # Decrypt credentials (implement proper encryption in production)
                    decrypted_password = self._decrypt_password(account.encrypted_password)
                    
                    # Reconnect to MetaApi
                    await self.metaapi_client.add_subscriber_account(
                        account_login=account.login,
                        account_password=decrypted_password,
                        account_server=account.server,
                        user_id=str(account.user_id)
                    )
                    
                    logger.info(f"Reconnected MT5 account: {account.login}")
                    
                except Exception as e:
                    logger.error(f"Failed to reconnect account {account.login}: {e}")
                    # Mark account as disconnected in database
                    await self.db_manager.update_account_status(account.id, 'disconnected')
            
            logger.info(f"Loaded {len(accounts)} existing accounts")
            
        except Exception as e:
            logger.error(f"Error loading existing accounts: {e}")
    
    async def connect_user_account(self, user_id: int, account_login: str, 
                                 account_password: str, account_server: str) -> Dict[str, Any]:
        """
        Connect a user's MT5 account for copy trading
        
        Args:
            user_id: User ID
            account_login: MT5 account login
            account_password: MT5 account password
            account_server: MT5 server name
            
        Returns:
            Connection result dictionary
        """
        try:
            logger.info(f"Connecting MT5 account for user {user_id}")
            
            # Check if account already exists
            existing_account = await self.db_manager.get_mt5_account_by_login(account_login)
            if existing_account:
                return {
                    'success': False,
                    'error': 'Account already connected',
                    'account_id': existing_account.id
                }
            
            # Validate account credentials by connecting
            account_data = await self.metaapi_client.add_subscriber_account(
                account_login=account_login,
                account_password=account_password,
                account_server=account_server,
                user_id=str(user_id)
            )
            
            # Encrypt password for storage (implement proper encryption)
            encrypted_password = self._encrypt_password(account_password)
            
            # Save account to database
            mt5_account = await self.db_manager.create_mt5_account(
                user_id=user_id,
                login=account_login,
                server=account_server,
                encrypted_password=encrypted_password,
                account_name=f"MT5 Account - {account_login}",
                metaapi_account_id=account_data['account_id']
            )
            
            logger.info(f"MT5 account connected successfully: {account_login}")
            
            return {
                'success': True,
                'account_id': mt5_account.id,
                'metaapi_account_id': account_data['account_id'],
                'login': account_login,
                'server': account_server,
                'status': 'connected',
                'balance': account_data.get('account_info', {}).get('balance', 0),
                'equity': account_data.get('account_info', {}).get('equity', 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to connect user account: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def disconnect_user_account(self, user_id: int, account_id: int) -> Dict[str, Any]:
        """
        Disconnect a user's MT5 account
        
        Args:
            user_id: User ID
            account_id: MT5 account ID
            
        Returns:
            Disconnection result dictionary
        """
        try:
            logger.info(f"Disconnecting MT5 account {account_id} for user {user_id}")
            
            # Get account from database
            account = await self.db_manager.get_mt5_account(account_id)
            if not account or account.user_id != user_id:
                return {
                    'success': False,
                    'error': 'Account not found or access denied'
                }
            
            # Remove from MetaApi
            success = await self.metaapi_client.remove_subscriber_account(account.metaapi_account_id)
            
            if success:
                # Update database status
                await self.db_manager.update_account_status(account_id, 'disconnected')
                
                logger.info(f"MT5 account disconnected successfully: {account.login}")
                
                return {
                    'success': True,
                    'message': 'Account disconnected successfully'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to disconnect from MetaApi'
                }
                
        except Exception as e:
            logger.error(f"Failed to disconnect user account: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_user_accounts(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all MT5 accounts for a user"""
        try:
            accounts = await self.db_manager.get_user_mt5_accounts(user_id)
            
            result = []
            for account in accounts:
                # Get live account info from MetaApi if connected
                account_info = {}
                if account.status == 'connected' and account.metaapi_account_id:
                    try:
                        account_info = await self.metaapi_client.get_account_info(account.metaapi_account_id)
                    except Exception as e:
                        logger.warning(f"Could not get live info for account {account.login}: {e}")
                
                result.append({
                    'id': account.id,
                    'login': account.login,
                    'server': account.server,
                    'account_name': account.account_name,
                    'status': account.status,
                    'created_at': account.created_at,
                    'balance': account_info.get('balance', 0),
                    'equity': account_info.get('equity', 0),
                    'currency': account_info.get('currency', 'USD'),
                    'leverage': account_info.get('leverage', 0),
                    'margin_level': account_info.get('margin_level', 0)
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting user accounts: {e}")
            return []
    
    async def get_account_performance(self, account_id: int) -> Dict[str, Any]:
        """Get performance metrics for an account"""
        try:
            # Get trades from database
            trades = await self.db_manager.get_account_trades(account_id)
            
            if not trades:
                return {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate': 0,
                    'total_profit': 0,
                    'average_profit': 0,
                    'max_profit': 0,
                    'max_loss': 0
                }
            
            # Calculate performance metrics
            total_trades = len(trades)
            winning_trades = sum(1 for trade in trades if trade.profit > 0)
            losing_trades = sum(1 for trade in trades if trade.profit < 0)
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            profits = [trade.profit for trade in trades if trade.profit is not None]
            total_profit = sum(profits)
            average_profit = total_profit / len(profits) if profits else 0
            max_profit = max(profits) if profits else 0
            max_loss = min(profits) if profits else 0
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': round(win_rate, 2),
                'total_profit': round(total_profit, 2),
                'average_profit': round(average_profit, 2),
                'max_profit': round(max_profit, 2),
                'max_loss': round(max_loss, 2)
            }
            
        except Exception as e:
            logger.error(f"Error getting account performance: {e}")
            return {}
    
    async def validate_account_credentials(self, login: str, password: str, server: str) -> bool:
        """Validate MT5 account credentials"""
        try:
            # This is a simplified validation - in production you might want to
            # create a temporary connection to validate credentials
            logger.info(f"Validating credentials for account {login}")
            
            # Basic validation
            if not login or not password or not server:
                return False
            
            # Check if login is numeric (MT5 accounts are typically numeric)
            if not login.isdigit():
                return False
            
            # Check server format
            if 'ThinkMarkets' not in server:
                logger.warning(f"Server {server} is not a ThinkMarkets server")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating account credentials: {e}")
            return False
    
    def _encrypt_password(self, password: str) -> str:
        """
        Encrypt password for storage
        NOTE: This is a simple implementation. Use proper encryption in production!
        """
        # In production, use proper encryption like Fernet from cryptography library
        return hashlib.sha256((password + settings.secret_key).encode()).hexdigest()
    
    def _decrypt_password(self, encrypted_password: str) -> str:
        """
        Decrypt password from storage
        NOTE: This is a placeholder. Implement proper decryption in production!
        """
        # This is a placeholder - you cannot decrypt a hash
        # In production, use proper symmetric encryption
        raise NotImplementedError("Proper encryption/decryption not implemented")
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get system-wide statistics"""
        try:
            # Get connected accounts info
            connected_accounts = self.metaapi_client.get_connected_accounts_info()
            
            # Get database stats
            total_users = await self.db_manager.get_total_users()
            total_accounts = await self.db_manager.get_total_mt5_accounts()
            active_accounts = await self.db_manager.get_active_mt5_accounts_count()
            total_trades = await self.db_manager.get_total_trades()
            
            return {
                'connected_accounts': len(connected_accounts),
                'total_users': total_users,
                'total_accounts': total_accounts,
                'active_accounts': active_accounts,
                'total_trades': total_trades,
                'master_account_connected': self.metaapi_client.master_account is not None,
                'system_status': 'operational' if self.is_initialized else 'initializing'
            }
            
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {}
    
    async def shutdown(self):
        """Shutdown the account manager"""
        try:
            logger.info("Shutting down Account Manager...")
            
            # Shutdown MetaApi client
            await self.metaapi_client.shutdown()
            
            # Close database connections
            await self.db_manager.close()
            
            self.is_initialized = False
            logger.info("Account Manager shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during Account Manager shutdown: {e}")
