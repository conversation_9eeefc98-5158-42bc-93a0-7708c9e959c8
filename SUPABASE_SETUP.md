# 🚀 Supabase PostgreSQL Setup Guide

This guide will help you set up PostgreSQL using Supabase for your AI Forex Trading Platform.

## Step 1: Create Supabase Account

1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project" 
3. Sign up with GitHub, Google, or email
4. Verify your email if needed

## Step 2: Create New Project

1. Click "New Project" in your dashboard
2. Choose your organization (or create one)
3. Fill in project details:
   - **Name**: `ai-forex-trading`
   - **Database Password**: Generate a strong password (save this!)
   - **Region**: Choose closest to your location
   - **Pricing Plan**: Start with "Free" (perfect for development)

4. Click "Create new project"
5. Wait 2-3 minutes for setup to complete

## Step 3: Get Database Connection Details

1. In your Supabase project dashboard, go to **Settings** → **Database**
2. Scroll down to "Connection string" section
3. Copy the **URI** connection string (it looks like):
   ```
   postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres
   ```

## Step 4: Update Your Environment Configuration

1. Create or update your `.env` file in the project root:
   ```env
   # Database Configuration
   DATABASE_URL=postgresql+asyncpg://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres
   
   # Security
   SECRET_KEY=your-super-secret-key-here-make-it-long-and-random
   
   # MetaApi Configuration
   METAAPI_TOKEN=your-metaapi-token-here
   
   # Application Settings
   ENVIRONMENT=development
   DEBUG=true
   LOG_LEVEL=INFO
   
   # API Settings
   API_HOST=0.0.0.0
   API_PORT=8000
   API_RELOAD=true
   
   # JWT Settings
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   ```

2. **Important**: Replace the placeholders:
   - `[YOUR-PASSWORD]`: Your database password from Step 2
   - `[PROJECT-REF]`: Your project reference from the connection string
   - `your-super-secret-key-here-make-it-long-and-random`: Generate a random secret key
   - `your-metaapi-token-here`: Your MetaApi token

## Step 5: Install Dependencies

```bash
# Install database dependencies
pip install -r requirements-web.txt

# Install Alembic for migrations
pip install alembic
```

## Step 6: Set Up Database Schema

```bash
# Run the database setup script
python setup_database.py
```

This script will:
- ✅ Test your database connection
- ✅ Run all database migrations
- ✅ Create the required tables
- ✅ Optionally create sample test data

## Step 7: Start Your Application

```bash
# Start the web server
python main.py
```

Your application will be available at:
- **Dashboard**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Login Page**: http://localhost:8000/login.html

## 🔧 Supabase Dashboard Features

Your Supabase project includes:

### **Table Editor**
- View and edit your data directly
- Located at: Project → Table Editor

### **SQL Editor** 
- Run custom SQL queries
- Located at: Project → SQL Editor

### **Database Backups**
- Automatic daily backups (Free tier: 7 days retention)
- Located at: Settings → Database → Backups

### **Real-time Subscriptions**
- Monitor live changes to your data
- Perfect for real-time trading updates

### **Row Level Security (RLS)**
- Advanced security for user data isolation
- Can be configured in Authentication → Policies

## 🚨 Important Security Notes

1. **Never commit your `.env` file** to version control
2. **Use strong passwords** for your database
3. **Enable Row Level Security** for production
4. **Regularly rotate your SECRET_KEY**

## 📊 Monitoring Your Database

### View Tables in Supabase:
1. Go to **Table Editor** in your Supabase dashboard
2. You should see these tables:
   - `users` - User accounts
   - `mt5_accounts` - MT5 trading accounts
   - `trades` - Trade history
   - `trading_signals` - AI-generated signals
   - `ai_models` - ML model versions
   - `system_logs` - Application logs
   - `market_data` - Historical market data

### Monitor Performance:
1. Go to **Reports** in your Supabase dashboard
2. View database performance metrics
3. Monitor API usage and query performance

## 🆘 Troubleshooting

### Connection Issues:
```bash
# Test connection manually
python -c "
import asyncio
from setup_database import test_connection
asyncio.run(test_connection())
"
```

### Migration Issues:
```bash
# Reset migrations (CAUTION: This will delete all data)
python -c "
from alembic.config import Config
from alembic import command
cfg = Config('alembic.ini')
command.downgrade(cfg, 'base')
command.upgrade(cfg, 'head')
"
```

### View Logs:
```bash
# Check application logs
tail -f logs/app.log
```

## 🎯 Next Steps

Once your database is set up:

1. **Test the web interface** at http://localhost:8000
2. **Register a new user account**
3. **Connect a demo MT5 account**
4. **Monitor the database** in Supabase dashboard
5. **Review the API documentation** at http://localhost:8000/docs

## 💡 Pro Tips

- **Free Tier Limits**: 500MB database, 2GB bandwidth/month
- **Upgrade Path**: Easy scaling to paid tiers when needed
- **Backup Strategy**: Export important data regularly
- **Performance**: Use indexes for frequently queried columns
- **Security**: Enable RLS before going to production

Your AI Forex Trading Platform is now ready with a robust PostgreSQL database! 🎉
