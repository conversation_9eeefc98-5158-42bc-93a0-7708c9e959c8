"""
FastAPI server for the AI Forex Trading Platform dashboard
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware

from config import settings
from src.ai_trading.utils.logger import get_logger

logger = get_logger(__name__)


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="AI Forex Trading Platform",
        description="AI-powered autotrading platform with MetaApi integration",
        version="1.0.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.debug else ["https://yourdomain.com"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Static files and templates
    app.mount("/static", StaticFiles(directory="static"), name="static")
    templates = Jinja2Templates(directory="templates")
    
    # Include routers
    from src.ai_trading.api.routes import auth, dashboard, trading, accounts
    
    app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
    app.include_router(dashboard.router, prefix="/api/dashboard", tags=["Dashboard"])
    app.include_router(trading.router, prefix="/api/trading", tags=["Trading"])
    app.include_router(accounts.router, prefix="/api/accounts", tags=["Accounts"])
    
    @app.get("/")
    async def root():
        """Root endpoint"""
        return {
            "message": "AI Forex Trading Platform API",
            "version": "1.0.0",
            "status": "running"
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z"  # TODO: Use actual timestamp
        }
    
    logger.info("FastAPI application created")
    return app
