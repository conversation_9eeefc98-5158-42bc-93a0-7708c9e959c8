# MetaApi Configuration
METAAPI_TOKEN=your_metaapi_token_here
MASTER_ACCOUNT_ID=your_master_mt5_account_id
MASTER_ACCOUNT_PASSWORD=your_master_account_password
MASTER_ACCOUNT_SERVER=ThinkMarkets-Demo  # or ThinkMarkets-Live

# Database Configuration (REQUIRED)
# Supabase PostgreSQL connection string (recommended)
DATABASE_URL=postgresql+asyncpg://postgres:<EMAIL>:5432/postgres
# Alternative: Local PostgreSQL
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/forex_trading_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=forex_trading_db
DATABASE_USER=username
DATABASE_PASSWORD=password

# Security
SECRET_KEY=your_super_secret_key_here_change_this_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Trading Configuration
DEFAULT_RISK_PERCENT=1.0  # Risk per trade as percentage of account
MAX_CONCURRENT_TRADES=5
VOLATILITY_THRESHOLD=0.7  # AI volatility filter threshold (0-1)

# AI Model Configuration
MODEL_UPDATE_INTERVAL_HOURS=24
FEATURE_LOOKBACK_PERIODS=20
TRAINING_DATA_DAYS=365

# Logging
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/trading_bot.log

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=True  # Set to False in production

# Notification Settings (Optional)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Development Settings
ENVIRONMENT=development  # development, staging, production
DEBUG=True
