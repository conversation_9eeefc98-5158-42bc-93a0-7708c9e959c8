"""Initial database schema for AI Forex Trading Platform

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create users table
    op.create_table('users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=50), nullable=False),
        sa.Column('email', sa.String(length=100), nullable=False),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('full_name', sa.String(length=100), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('is_verified', sa.<PERSON>(), nullable=True, default=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)

    # Create mt5_accounts table
    op.create_table('mt5_accounts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('login', sa.String(length=50), nullable=False),
        sa.Column('server', sa.String(length=100), nullable=False),
        sa.Column('encrypted_password', sa.Text(), nullable=False),
        sa.Column('account_name', sa.String(length=100), nullable=True),
        sa.Column('metaapi_account_id', sa.String(length=100), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=True, default='pending'),
        sa.Column('balance', sa.Numeric(precision=15, scale=2), nullable=True, default=0.0),
        sa.Column('equity', sa.Numeric(precision=15, scale=2), nullable=True, default=0.0),
        sa.Column('margin', sa.Numeric(precision=15, scale=2), nullable=True, default=0.0),
        sa.Column('free_margin', sa.Numeric(precision=15, scale=2), nullable=True, default=0.0),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_mt5_accounts_login'), 'mt5_accounts', ['login'], unique=True)
    op.create_index(op.f('ix_mt5_accounts_metaapi_account_id'), 'mt5_accounts', ['metaapi_account_id'], unique=True)
    op.create_index(op.f('ix_mt5_accounts_user_id'), 'mt5_accounts', ['user_id'])

    # Create trades table
    op.create_table('trades',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('mt5_account_id', sa.Integer(), nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('trade_type', sa.String(length=10), nullable=False),
        sa.Column('volume', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('open_price', sa.Numeric(precision=15, scale=5), nullable=True),
        sa.Column('close_price', sa.Numeric(precision=15, scale=5), nullable=True),
        sa.Column('stop_loss', sa.Numeric(precision=15, scale=5), nullable=True),
        sa.Column('take_profit', sa.Numeric(precision=15, scale=5), nullable=True),
        sa.Column('profit', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('commission', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('swap', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True, default='pending'),
        sa.Column('mt5_position_id', sa.String(length=50), nullable=True),
        sa.Column('signal_id', sa.Integer(), nullable=True),
        sa.Column('opened_at', sa.DateTime(), nullable=True),
        sa.Column('closed_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['mt5_account_id'], ['mt5_accounts.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_trades_mt5_account_id'), 'trades', ['mt5_account_id'])
    op.create_index(op.f('ix_trades_symbol'), 'trades', ['symbol'])
    op.create_index(op.f('ix_trades_user_id'), 'trades', ['user_id'])
    op.create_index(op.f('ix_trades_created_at'), 'trades', ['created_at'])

    # Create trading_signals table
    op.create_table('trading_signals',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('action', sa.String(length=10), nullable=False),
        sa.Column('confidence', sa.Numeric(precision=5, scale=4), nullable=False),
        sa.Column('entry_price', sa.Numeric(precision=15, scale=5), nullable=True),
        sa.Column('stop_loss', sa.Numeric(precision=15, scale=5), nullable=True),
        sa.Column('take_profit', sa.Numeric(precision=15, scale=5), nullable=True),
        sa.Column('volume', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True, default='active'),
        sa.Column('model_version', sa.String(length=50), nullable=True),
        sa.Column('market_conditions', sa.JSON(), nullable=True),
        sa.Column('generated_at', sa.DateTime(), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_trading_signals_symbol'), 'trading_signals', ['symbol'])
    op.create_index(op.f('ix_trading_signals_status'), 'trading_signals', ['status'])
    op.create_index(op.f('ix_trading_signals_generated_at'), 'trading_signals', ['generated_at'])

    # Create ai_models table
    op.create_table('ai_models',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('version', sa.String(length=50), nullable=False),
        sa.Column('model_type', sa.String(length=50), nullable=False),
        sa.Column('file_path', sa.String(length=255), nullable=True),
        sa.Column('parameters', sa.JSON(), nullable=True),
        sa.Column('performance_metrics', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=False),
        sa.Column('trained_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_models_name'), 'ai_models', ['name'])
    op.create_index(op.f('ix_ai_models_is_active'), 'ai_models', ['is_active'])

    # Create system_logs table
    op.create_table('system_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('level', sa.String(length=20), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('module', sa.String(length=100), nullable=True),
        sa.Column('function', sa.String(length=100), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('additional_data', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_logs_level'), 'system_logs', ['level'])
    op.create_index(op.f('ix_system_logs_created_at'), 'system_logs', ['created_at'])

    # Create market_data table
    op.create_table('market_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('timeframe', sa.String(length=10), nullable=False),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('open_price', sa.Numeric(precision=15, scale=5), nullable=False),
        sa.Column('high_price', sa.Numeric(precision=15, scale=5), nullable=False),
        sa.Column('low_price', sa.Numeric(precision=15, scale=5), nullable=False),
        sa.Column('close_price', sa.Numeric(precision=15, scale=5), nullable=False),
        sa.Column('volume', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_market_data_symbol'), 'market_data', ['symbol'])
    op.create_index(op.f('ix_market_data_timestamp'), 'market_data', ['timestamp'])
    op.create_index('ix_market_data_symbol_timeframe_timestamp', 'market_data', ['symbol', 'timeframe', 'timestamp'], unique=True)


def downgrade() -> None:
    op.drop_table('market_data')
    op.drop_table('system_logs')
    op.drop_table('ai_models')
    op.drop_table('trading_signals')
    op.drop_table('trades')
    op.drop_table('mt5_accounts')
    op.drop_table('users')
