"""
Dashboard routes
"""
from fastapi import APIRouter

router = APIRouter()


@router.get("/stats")
async def get_dashboard_stats():
    """Get dashboard statistics"""
    # TODO: Implement dashboard stats
    return {"message": "Dashboard stats - TODO"}


@router.get("/performance")
async def get_performance_metrics():
    """Get performance metrics"""
    # TODO: Implement performance metrics
    return {"message": "Performance metrics - TODO"}
