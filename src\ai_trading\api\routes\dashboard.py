"""
Dashboard routes for trading performance and statistics
"""
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, status, Depends, Query
from pydantic import BaseModel

from src.ai_trading.api.auth import get_current_active_user
from src.ai_trading.database.models import User
from src.ai_trading.database.database import DatabaseManager
from src.ai_trading.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


# Pydantic models
class DashboardStats(BaseModel):
    total_accounts: int
    active_accounts: int
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_profit: float
    total_loss: float
    net_profit: float
    win_rate: float
    profit_factor: float
    average_win: float
    average_loss: float
    largest_win: float
    largest_loss: float
    current_drawdown: float
    max_drawdown: float


class AccountPerformance(BaseModel):
    account_id: int
    account_name: Optional[str]
    login: str
    balance: float
    equity: float
    profit: float
    trades_count: int
    win_rate: float
    status: str


class TradeHistory(BaseModel):
    id: int
    account_id: int
    symbol: str
    trade_type: str
    volume: float
    open_price: float
    close_price: Optional[float]
    profit: Optional[float]
    open_time: str
    close_time: Optional[str]
    status: str


class PerformanceMetrics(BaseModel):
    period: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    profit: float
    loss: float
    net_profit: float
    profit_factor: float
    sharpe_ratio: Optional[float]
    max_drawdown: float
    recovery_factor: Optional[float]


@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(current_user: User = Depends(get_current_active_user)):
    """Get comprehensive dashboard statistics"""
    try:
        db_manager = DatabaseManager()
        await db_manager.initialize()

        # Get user's MT5 accounts
        accounts = await db_manager.get_user_mt5_accounts(current_user.id)

        # Get all trades for user's accounts
        all_trades = []
        for account in accounts:
            trades = await db_manager.get_account_trades(account.id)
            all_trades.extend(trades)

        # Calculate statistics
        total_accounts = len(accounts)
        active_accounts = len([acc for acc in accounts if acc.status == "connected"])
        total_trades = len(all_trades)

        # Trade analysis
        closed_trades = [trade for trade in all_trades if trade.status == "closed" and trade.profit is not None]
        winning_trades = len([trade for trade in closed_trades if trade.profit > 0])
        losing_trades = len([trade for trade in closed_trades if trade.profit < 0])

        # Profit calculations
        profits = [trade.profit for trade in closed_trades if trade.profit > 0]
        losses = [trade.profit for trade in closed_trades if trade.profit < 0]

        total_profit = sum(profits) if profits else 0.0
        total_loss = abs(sum(losses)) if losses else 0.0
        net_profit = total_profit - total_loss

        # Performance metrics
        win_rate = (winning_trades / len(closed_trades) * 100) if closed_trades else 0.0
        profit_factor = (total_profit / total_loss) if total_loss > 0 else 0.0
        average_win = (total_profit / winning_trades) if winning_trades > 0 else 0.0
        average_loss = (total_loss / losing_trades) if losing_trades > 0 else 0.0
        largest_win = max(profits) if profits else 0.0
        largest_loss = abs(min(losses)) if losses else 0.0

        # Drawdown calculation (simplified)
        current_drawdown = 0.0  # Would need equity curve for accurate calculation
        max_drawdown = 0.0      # Would need historical equity data

        return DashboardStats(
            total_accounts=total_accounts,
            active_accounts=active_accounts,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            total_profit=total_profit,
            total_loss=total_loss,
            net_profit=net_profit,
            win_rate=win_rate,
            profit_factor=profit_factor,
            average_win=average_win,
            average_loss=average_loss,
            largest_win=largest_win,
            largest_loss=largest_loss,
            current_drawdown=current_drawdown,
            max_drawdown=max_drawdown
        )

    except Exception as e:
        logger.error(f"Error getting dashboard stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard statistics"
        )


@router.get("/accounts-performance", response_model=List[AccountPerformance])
async def get_accounts_performance(current_user: User = Depends(get_current_active_user)):
    """Get performance metrics for all user accounts"""
    try:
        db_manager = DatabaseManager()
        await db_manager.initialize()

        # Get user's MT5 accounts
        accounts = await db_manager.get_user_mt5_accounts(current_user.id)

        performance_list = []
        for account in accounts:
            # Get account trades
            trades = await db_manager.get_account_trades(account.id)
            closed_trades = [trade for trade in trades if trade.status == "closed" and trade.profit is not None]

            # Calculate performance
            total_profit = sum(trade.profit for trade in closed_trades)
            winning_trades = len([trade for trade in closed_trades if trade.profit > 0])
            win_rate = (winning_trades / len(closed_trades) * 100) if closed_trades else 0.0

            performance_list.append(AccountPerformance(
                account_id=account.id,
                account_name=account.account_name,
                login=account.login,
                balance=account.last_balance,
                equity=account.last_equity,
                profit=total_profit,
                trades_count=len(trades),
                win_rate=win_rate,
                status=account.status
            ))

        return performance_list

    except Exception as e:
        logger.error(f"Error getting accounts performance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve accounts performance"
        )


@router.get("/trades", response_model=List[TradeHistory])
async def get_trade_history(
    current_user: User = Depends(get_current_active_user),
    account_id: Optional[int] = Query(None, description="Filter by account ID"),
    limit: int = Query(100, description="Number of trades to return"),
    offset: int = Query(0, description="Number of trades to skip")
):
    """Get trade history for user accounts"""
    try:
        db_manager = DatabaseManager()
        await db_manager.initialize()

        # Get user's MT5 accounts
        if account_id:
            # Verify account belongs to user
            account = await db_manager.get_mt5_account(account_id)
            if not account or account.user_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Account not found"
                )
            accounts = [account]
        else:
            accounts = await db_manager.get_user_mt5_accounts(current_user.id)

        # Get trades
        all_trades = []
        for account in accounts:
            trades = await db_manager.get_account_trades(account.id, limit=limit, offset=offset)
            for trade in trades:
                all_trades.append(TradeHistory(
                    id=trade.id,
                    account_id=trade.account_id,
                    symbol=trade.symbol,
                    trade_type=trade.trade_type,
                    volume=trade.volume,
                    open_price=trade.open_price,
                    close_price=trade.close_price,
                    profit=trade.profit,
                    open_time=trade.open_time.isoformat(),
                    close_time=trade.close_time.isoformat() if trade.close_time else None,
                    status=trade.status
                ))

        # Sort by open time (most recent first)
        all_trades.sort(key=lambda x: x.open_time, reverse=True)

        return all_trades[:limit]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting trade history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve trade history"
        )


@router.get("/performance", response_model=PerformanceMetrics)
async def get_performance_metrics(
    current_user: User = Depends(get_current_active_user),
    period: str = Query("30d", description="Time period: 7d, 30d, 90d, 1y, all"),
    account_id: Optional[int] = Query(None, description="Filter by account ID")
):
    """Get performance metrics for specified period"""
    try:
        db_manager = DatabaseManager()
        await db_manager.initialize()

        # Calculate date range
        end_date = datetime.utcnow()
        if period == "7d":
            start_date = end_date - timedelta(days=7)
        elif period == "30d":
            start_date = end_date - timedelta(days=30)
        elif period == "90d":
            start_date = end_date - timedelta(days=90)
        elif period == "1y":
            start_date = end_date - timedelta(days=365)
        else:  # "all"
            start_date = datetime(2020, 1, 1)  # Far back date

        # Get user's accounts
        if account_id:
            account = await db_manager.get_mt5_account(account_id)
            if not account or account.user_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Account not found"
                )
            accounts = [account]
        else:
            accounts = await db_manager.get_user_mt5_accounts(current_user.id)

        # Get trades in period
        all_trades = []
        for account in accounts:
            trades = await db_manager.get_account_trades_in_period(
                account.id, start_date, end_date
            )
            all_trades.extend(trades)

        # Calculate metrics
        closed_trades = [trade for trade in all_trades if trade.status == "closed" and trade.profit is not None]
        total_trades = len(closed_trades)

        if total_trades == 0:
            return PerformanceMetrics(
                period=period,
                total_trades=0,
                winning_trades=0,
                losing_trades=0,
                win_rate=0.0,
                profit=0.0,
                loss=0.0,
                net_profit=0.0,
                profit_factor=0.0,
                sharpe_ratio=None,
                max_drawdown=0.0,
                recovery_factor=None
            )

        winning_trades = len([trade for trade in closed_trades if trade.profit > 0])
        losing_trades = len([trade for trade in closed_trades if trade.profit < 0])

        profits = [trade.profit for trade in closed_trades if trade.profit > 0]
        losses = [trade.profit for trade in closed_trades if trade.profit < 0]

        total_profit = sum(profits) if profits else 0.0
        total_loss = abs(sum(losses)) if losses else 0.0
        net_profit = total_profit - total_loss

        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0.0
        profit_factor = (total_profit / total_loss) if total_loss > 0 else 0.0

        # Advanced metrics (simplified calculations)
        sharpe_ratio = None  # Would need risk-free rate and return volatility
        max_drawdown = 0.0   # Would need equity curve
        recovery_factor = None  # Would need max drawdown calculation

        return PerformanceMetrics(
            period=period,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            profit=total_profit,
            loss=total_loss,
            net_profit=net_profit,
            profit_factor=profit_factor,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            recovery_factor=recovery_factor
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance metrics"
        )


@router.get("/signals", response_model=List[Dict[str, Any]])
async def get_recent_signals(
    current_user: User = Depends(get_current_active_user),
    limit: int = Query(50, description="Number of signals to return")
):
    """Get recent trading signals"""
    try:
        db_manager = DatabaseManager()
        await db_manager.initialize()

        # Get recent signals
        signals = await db_manager.get_recent_trading_signals(limit=limit)

        # Format signals
        signal_list = []
        for signal in signals:
            signal_list.append({
                "id": signal.id,
                "symbol": signal.symbol,
                "signal_type": signal.signal_type,
                "confidence": signal.confidence,
                "entry_price": signal.entry_price,
                "stop_loss": signal.stop_loss,
                "take_profit": signal.take_profit,
                "created_at": signal.created_at.isoformat(),
                "status": signal.status
            })

        return signal_list

    except Exception as e:
        logger.error(f"Error getting recent signals: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve recent signals"
        )
