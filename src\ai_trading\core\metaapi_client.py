"""
MetaApi client for MT5 integration
Handles connection, account management, and trade execution
"""
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import pandas as pd

from metaapi_cloud_sdk import MetaApi, CopyFactory
from metaapi_cloud_sdk.clients.metaApi.models import MetatraderAccountInformation

from config import settings, TradingConfig
from src.ai_trading.utils.logger import get_logger

logger = get_logger(__name__)


class MetaApiClient:
    """MetaApi client for MT5 trading operations"""
    
    def __init__(self):
        self.api = None
        self.copy_factory = None
        self.master_account = None
        self.connected_accounts: Dict[str, Any] = {}
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize MetaApi connection"""
        try:
            logger.info("Initializing MetaApi client...")
            
            # Initialize MetaApi
            self.api = MetaApi(settings.metaapi_token)
            
            # Initialize CopyFactory for trade copying
            self.copy_factory = CopyFactory(settings.metaapi_token)
            
            # Connect to master account
            await self._connect_master_account()
            
            self.is_initialized = True
            logger.info("MetaApi client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize MetaApi client: {e}")
            raise
    
    async def _connect_master_account(self):
        """Connect to the master trading account"""
        try:
            logger.info("Connecting to master account...")
            
            # Get master account
            accounts = await self.api.metatrader_account_api.get_accounts()
            master_account = None
            
            for account in accounts:
                if account.id == settings.master_account_id:
                    master_account = account
                    break
            
            if not master_account:
                # Create master account if it doesn't exist
                logger.info("Creating master account...")
                master_account = await self.api.metatrader_account_api.create_account({
                    'name': 'Master Trading Account',
                    'type': 'cloud',
                    'login': settings.master_account_id,
                    'password': settings.master_account_password,
                    'server': settings.master_account_server,
                    'platform': 'mt5',
                    'magic': 123456,
                    'application': 'MetaApi',
                    'copyFactoryRoles': ['PROVIDER']  # Set as copy factory provider
                })
            
            # Wait for account to be deployed
            logger.info("Waiting for master account deployment...")
            await master_account.wait_deployed()
            
            # Connect to account
            connection = master_account.get_rpc_connection()
            await connection.connect()
            await connection.wait_synchronized()
            
            self.master_account = master_account
            logger.info(f"Master account connected: {master_account.id}")
            
        except Exception as e:
            logger.error(f"Failed to connect master account: {e}")
            raise
    
    async def add_subscriber_account(self, account_login: str, account_password: str, 
                                   account_server: str, user_id: str) -> Dict[str, Any]:
        """
        Add a subscriber account for copy trading
        
        Args:
            account_login: MT5 account login
            account_password: MT5 account password
            account_server: MT5 server name
            user_id: User identifier
            
        Returns:
            Account information dictionary
        """
        try:
            logger.info(f"Adding subscriber account for user {user_id}")
            
            # Create account
            account = await self.api.metatrader_account_api.create_account({
                'name': f'User Account - {user_id}',
                'type': 'cloud',
                'login': account_login,
                'password': account_password,
                'server': account_server,
                'platform': 'mt5',
                'application': 'MetaApi',
                'copyFactoryRoles': ['SUBSCRIBER']  # Set as copy factory subscriber
            })
            
            # Wait for deployment
            await account.wait_deployed()
            
            # Connect to account
            connection = account.get_rpc_connection()
            await connection.connect()
            await connection.wait_synchronized()
            
            # Get account information
            account_info = await connection.get_account_information()
            
            # Store account
            account_data = {
                'account_id': account.id,
                'user_id': user_id,
                'login': account_login,
                'server': account_server,
                'connection': connection,
                'account_info': account_info,
                'status': 'connected',
                'created_at': datetime.now()
            }
            
            self.connected_accounts[account.id] = account_data
            
            # Set up copy trading subscription
            await self._setup_copy_trading_subscription(account.id)
            
            logger.info(f"Subscriber account added successfully: {account.id}")
            return account_data
            
        except Exception as e:
            logger.error(f"Failed to add subscriber account: {e}")
            raise
    
    async def _setup_copy_trading_subscription(self, subscriber_account_id: str):
        """Set up copy trading subscription for an account"""
        try:
            # Create copy trading subscription
            await self.copy_factory.configuration_api.update_subscription(
                subscriber_account_id,
                {
                    'name': f'Subscription for {subscriber_account_id}',
                    'providers': [{
                        'id': settings.master_account_id,
                        'copyStopLoss': True,
                        'copyTakeProfit': True,
                        'riskLimit': {
                            'type': 'balance-percentage',
                            'applyTo': 'balance',
                            'maxRisk': settings.default_risk_percent
                        }
                    }]
                }
            )
            
            logger.info(f"Copy trading subscription set up for account {subscriber_account_id}")
            
        except Exception as e:
            logger.error(f"Failed to set up copy trading subscription: {e}")
            raise
    
    async def remove_subscriber_account(self, account_id: str) -> bool:
        """Remove a subscriber account"""
        try:
            logger.info(f"Removing subscriber account: {account_id}")
            
            if account_id in self.connected_accounts:
                # Close connection
                connection = self.connected_accounts[account_id]['connection']
                connection.close()
                
                # Remove from connected accounts
                del self.connected_accounts[account_id]
            
            # Remove copy trading subscription
            await self.copy_factory.configuration_api.remove_subscription(account_id)
            
            # Remove account from MetaApi
            account = await self.api.metatrader_account_api.get_account(account_id)
            await account.remove()
            
            logger.info(f"Subscriber account removed: {account_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove subscriber account: {e}")
            return False
    
    async def execute_trade(self, symbol: str, action: str, volume: float, 
                          stop_loss: Optional[float] = None, 
                          take_profit: Optional[float] = None,
                          comment: str = "AI Trading Bot") -> Dict[str, Any]:
        """
        Execute a trade on the master account
        
        Args:
            symbol: Trading symbol (e.g., 'XAUUSD')
            action: 'buy' or 'sell'
            volume: Trade volume in lots
            stop_loss: Stop loss price (optional)
            take_profit: Take profit price (optional)
            comment: Trade comment
            
        Returns:
            Trade execution result
        """
        try:
            if not self.master_account:
                raise ValueError("Master account not connected")
            
            logger.info(f"Executing trade: {action.upper()} {volume} lots of {symbol}")
            
            connection = self.master_account.get_rpc_connection()
            
            # Prepare trade request
            trade_request = {
                'actionType': 'ORDER_TYPE_BUY' if action.lower() == 'buy' else 'ORDER_TYPE_SELL',
                'symbol': symbol,
                'volume': volume,
                'comment': comment
            }
            
            # Add stop loss and take profit if provided
            if stop_loss:
                trade_request['stopLoss'] = stop_loss
            if take_profit:
                trade_request['takeProfit'] = take_profit
            
            # Execute trade
            result = await connection.create_market_order(trade_request)
            
            if result['stringCode'] == 'TRADE_RETCODE_DONE':
                logger.info(f"Trade executed successfully: {result}")
                
                return {
                    'success': True,
                    'order_id': result.get('orderId'),
                    'position_id': result.get('positionId'),
                    'symbol': symbol,
                    'action': action,
                    'volume': volume,
                    'price': result.get('price'),
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'comment': comment,
                    'timestamp': datetime.now()
                }
            else:
                logger.error(f"Trade execution failed: {result}")
                return {
                    'success': False,
                    'error': result.get('description', 'Unknown error'),
                    'code': result.get('stringCode')
                }
                
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_market_data(self, symbol: str, timeframe: str = 'M5', 
                            count: int = 100) -> pd.DataFrame:
        """
        Get historical market data
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe (M1, M5, M15, M30, H1, H4, D1)
            count: Number of candles to retrieve
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            if not self.master_account:
                raise ValueError("Master account not connected")
            
            connection = self.master_account.get_rpc_connection()
            
            # Get candles
            candles = await connection.get_candles(symbol, timeframe, count)
            
            if not candles:
                logger.warning(f"No market data received for {symbol}")
                return pd.DataFrame()
            
            # Convert to DataFrame
            data = []
            for candle in candles:
                data.append({
                    'timestamp': candle['time'],
                    'open': candle['open'],
                    'high': candle['high'],
                    'low': candle['low'],
                    'close': candle['close'],
                    'volume': candle.get('tickVolume', 0)
                })
            
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            logger.debug(f"Retrieved {len(df)} candles for {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return pd.DataFrame()
    
    async def get_account_info(self, account_id: Optional[str] = None) -> Dict[str, Any]:
        """Get account information"""
        try:
            if account_id and account_id in self.connected_accounts:
                connection = self.connected_accounts[account_id]['connection']
            elif self.master_account:
                connection = self.master_account.get_rpc_connection()
            else:
                raise ValueError("No account connection available")
            
            account_info = await connection.get_account_information()
            
            return {
                'balance': account_info.balance,
                'equity': account_info.equity,
                'margin': account_info.margin,
                'free_margin': account_info.margin_free,
                'margin_level': account_info.margin_level,
                'currency': account_info.currency,
                'leverage': account_info.leverage,
                'server': account_info.server,
                'name': account_info.name
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {}
    
    async def get_open_positions(self, account_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get open positions"""
        try:
            if account_id and account_id in self.connected_accounts:
                connection = self.connected_accounts[account_id]['connection']
            elif self.master_account:
                connection = self.master_account.get_rpc_connection()
            else:
                raise ValueError("No account connection available")
            
            positions = await connection.get_positions()
            
            return [{
                'id': pos.id,
                'symbol': pos.symbol,
                'type': pos.type,
                'volume': pos.volume,
                'open_price': pos.open_price,
                'current_price': pos.current_price,
                'profit': pos.profit,
                'swap': pos.swap,
                'commission': pos.commission,
                'stop_loss': pos.stop_loss,
                'take_profit': pos.take_profit,
                'comment': pos.comment,
                'open_time': pos.time
            } for pos in positions]
            
        except Exception as e:
            logger.error(f"Error getting open positions: {e}")
            return []
    
    async def close_position(self, position_id: str, volume: Optional[float] = None) -> Dict[str, Any]:
        """Close a position"""
        try:
            if not self.master_account:
                raise ValueError("Master account not connected")
            
            connection = self.master_account.get_rpc_connection()
            
            # Close position
            result = await connection.close_position(position_id, volume)
            
            if result['stringCode'] == 'TRADE_RETCODE_DONE':
                logger.info(f"Position closed successfully: {position_id}")
                return {'success': True, 'result': result}
            else:
                logger.error(f"Failed to close position: {result}")
                return {'success': False, 'error': result.get('description')}
                
        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return {'success': False, 'error': str(e)}
    
    async def shutdown(self):
        """Shutdown MetaApi client"""
        try:
            logger.info("Shutting down MetaApi client...")
            
            # Close all subscriber connections
            for account_data in self.connected_accounts.values():
                try:
                    account_data['connection'].close()
                except Exception as e:
                    logger.warning(f"Error closing connection: {e}")
            
            # Close master account connection
            if self.master_account:
                try:
                    connection = self.master_account.get_rpc_connection()
                    connection.close()
                except Exception as e:
                    logger.warning(f"Error closing master connection: {e}")
            
            self.connected_accounts.clear()
            self.is_initialized = False
            
            logger.info("MetaApi client shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during MetaApi shutdown: {e}")
    
    def get_connected_accounts_info(self) -> List[Dict[str, Any]]:
        """Get information about all connected accounts"""
        accounts_info = []
        
        for account_id, account_data in self.connected_accounts.items():
            accounts_info.append({
                'account_id': account_id,
                'user_id': account_data['user_id'],
                'login': account_data['login'],
                'server': account_data['server'],
                'status': account_data['status'],
                'created_at': account_data['created_at'],
                'balance': account_data.get('account_info', {}).get('balance', 0),
                'equity': account_data.get('account_info', {}).get('equity', 0)
            })
        
        return accounts_info
