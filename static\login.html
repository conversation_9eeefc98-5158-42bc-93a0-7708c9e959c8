<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AI Forex Trading Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
        }

        body {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Se<PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
        }

        .auth-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }

        .auth-left {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }

        .auth-left h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .auth-left p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .auth-right {
            padding: 60px 40px;
        }

        .auth-form h3 {
            color: var(--primary-color);
            margin-bottom: 30px;
            font-weight: bold;
            text-align: center;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-outline-primary {
            border: 2px solid var(--secondary-color);
            color: var(--secondary-color);
            border-radius: 10px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
            transform: translateY(-2px);
        }

        .form-check-input:checked {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .text-link {
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .text-link:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }

        .loading .btn-text {
            display: none;
        }

        @media (max-width: 768px) {
            .auth-container {
                margin: 20px;
            }
            
            .auth-left, .auth-right {
                padding: 40px 30px;
            }
            
            .auth-left h2 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="row g-0">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 auth-left">
                <div>
                    <i class="fas fa-chart-line fa-4x mb-4"></i>
                    <h2>AI Forex Trading</h2>
                    <p>Advanced automated trading platform powered by artificial intelligence. Connect your MT5 accounts and let our AI handle your trades with precision and efficiency.</p>
                    <div class="mt-4">
                        <div class="d-flex justify-content-center gap-4">
                            <div class="text-center">
                                <i class="fas fa-robot fa-2x mb-2"></i>
                                <div>AI Powered</div>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                <div>Secure</div>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <div>24/7 Trading</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Forms -->
            <div class="col-lg-6 auth-right">
                <!-- Login Form -->
                <div id="loginForm" class="auth-form">
                    <h3>Welcome Back</h3>
                    
                    <div id="loginAlert"></div>
                    
                    <form onsubmit="handleLogin(event)">
                        <div class="mb-3">
                            <label for="loginEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="loginEmail" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">Password</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                Remember me
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3" id="loginBtn">
                            <span class="btn-text">Sign In</span>
                            <span class="loading-spinner spinner-border spinner-border-sm" role="status"></span>
                        </button>
                    </form>
                    
                    <div class="text-center">
                        <p class="mb-2">
                            <a href="#" class="text-link" onclick="showForgotPassword()">Forgot your password?</a>
                        </p>
                        <p>
                            Don't have an account? 
                            <a href="#" class="text-link" onclick="showRegister()">Sign up here</a>
                        </p>
                    </div>
                </div>

                <!-- Register Form -->
                <div id="registerForm" class="auth-form" style="display: none;">
                    <h3>Create Account</h3>
                    
                    <div id="registerAlert"></div>
                    
                    <form onsubmit="handleRegister(event)">
                        <div class="mb-3">
                            <label for="registerUsername" class="form-label">Username</label>
                            <input type="text" class="form-control" id="registerUsername" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="registerFullName" class="form-label">Full Name (Optional)</label>
                            <input type="text" class="form-control" id="registerFullName">
                        </div>
                        
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">Password</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                            <div class="form-text">
                                Password must be at least 8 characters with uppercase, lowercase, number, and special character.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                I agree to the <a href="#" class="text-link">Terms of Service</a> and <a href="#" class="text-link">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3" id="registerBtn">
                            <span class="btn-text">Create Account</span>
                            <span class="loading-spinner spinner-border spinner-border-sm" role="status"></span>
                        </button>
                    </form>
                    
                    <div class="text-center">
                        <p>
                            Already have an account? 
                            <a href="#" class="text-link" onclick="showLogin()">Sign in here</a>
                        </p>
                    </div>
                </div>

                <!-- Forgot Password Form -->
                <div id="forgotPasswordForm" class="auth-form" style="display: none;">
                    <h3>Reset Password</h3>
                    
                    <div id="forgotAlert"></div>
                    
                    <form onsubmit="handleForgotPassword(event)">
                        <div class="mb-3">
                            <label for="forgotEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="forgotEmail" required>
                            <div class="form-text">
                                Enter your email address and we'll send you a link to reset your password.
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3" id="forgotBtn">
                            <span class="btn-text">Send Reset Link</span>
                            <span class="loading-spinner spinner-border spinner-border-sm" role="status"></span>
                        </button>
                    </form>
                    
                    <div class="text-center">
                        <p>
                            Remember your password? 
                            <a href="#" class="text-link" onclick="showLogin()">Sign in here</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const API_BASE = '/api';

        // Check if already logged in
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('authToken');
            if (token) {
                window.location.href = '/';
            }
        });

        // Form switching functions
        function showLogin() {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('registerForm').style.display = 'none';
            document.getElementById('forgotPasswordForm').style.display = 'none';
        }

        function showRegister() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
            document.getElementById('forgotPasswordForm').style.display = 'none';
        }

        function showForgotPassword() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'none';
            document.getElementById('forgotPasswordForm').style.display = 'block';
        }

        // Utility functions
        function showAlert(containerId, message, type = 'danger') {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        function setLoading(buttonId, loading) {
            const button = document.getElementById(buttonId);
            if (loading) {
                button.classList.add('loading');
                button.disabled = true;
            } else {
                button.classList.remove('loading');
                button.disabled = false;
            }
        }

        // Login handler
        async function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            setLoading('loginBtn', true);
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('authToken', data.access_token);
                    showAlert('loginAlert', 'Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    showAlert('loginAlert', data.detail || 'Login failed');
                }
            } catch (error) {
                showAlert('loginAlert', 'Network error. Please try again.');
            } finally {
                setLoading('loginBtn', false);
            }
        }

        // Register handler
        async function handleRegister(event) {
            event.preventDefault();
            
            const username = document.getElementById('registerUsername').value;
            const email = document.getElementById('registerEmail').value;
            const fullName = document.getElementById('registerFullName').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (password !== confirmPassword) {
                showAlert('registerAlert', 'Passwords do not match');
                return;
            }
            
            setLoading('registerBtn', true);
            
            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        email,
                        password,
                        full_name: fullName || null
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('registerAlert', 'Registration successful! Please sign in.', 'success');
                    setTimeout(() => {
                        showLogin();
                    }, 2000);
                } else {
                    showAlert('registerAlert', data.detail || 'Registration failed');
                }
            } catch (error) {
                showAlert('registerAlert', 'Network error. Please try again.');
            } finally {
                setLoading('registerBtn', false);
            }
        }

        // Forgot password handler
        async function handleForgotPassword(event) {
            event.preventDefault();
            
            const email = document.getElementById('forgotEmail').value;
            
            setLoading('forgotBtn', true);
            
            try {
                const response = await fetch(`${API_BASE}/auth/forgot-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('forgotAlert', 'If the email exists, a reset link has been sent.', 'success');
                } else {
                    showAlert('forgotAlert', data.detail || 'Request failed');
                }
            } catch (error) {
                showAlert('forgotAlert', 'Network error. Please try again.');
            } finally {
                setLoading('forgotBtn', false);
            }
        }
    </script>
</body>
</html>
