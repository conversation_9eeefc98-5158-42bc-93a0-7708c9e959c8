"""
Test configuration and settings
"""
import pytest
from config import Settings, TradingConfig


def test_settings_validation():
    """Test settings validation"""
    # This will use default values from .env.example
    settings = Settings(
        metaapi_token="test_token",
        master_account_id="test_account",
        master_account_password="test_password",
        database_url="postgresql://test:test@localhost/test",
        database_user="test",
        database_password="test",
        secret_key="test_secret_key"
    )
    
    assert settings.metaapi_token == "test_token"
    assert settings.volatility_threshold == 0.7
    assert settings.default_risk_percent == 1.0


def test_trading_config():
    """Test trading configuration constants"""
    assert "XAUUSD" in TradingConfig.SUPPORTED_SYMBOLS
    assert "EURUSD" in TradingConfig.SUPPORTED_SYMBOLS
    assert TradingConfig.DEFAULT_TIMEFRAME == "M5"
    assert len(TradingConfig.VOLATILITY_FEATURES) == 7


def test_invalid_volatility_threshold():
    """Test invalid volatility threshold validation"""
    with pytest.raises(ValueError):
        Settings(
            metaapi_token="test",
            master_account_id="test",
            master_account_password="test",
            database_url="postgresql://test:test@localhost/test",
            database_user="test",
            database_password="test",
            secret_key="test",
            volatility_threshold=1.5  # Invalid - should be 0-1
        )
