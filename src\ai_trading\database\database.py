"""
Database manager for the AI Forex Trading Platform
"""
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import create_engine, select, func, and_, or_
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from contextlib import asynccontextmanager

from src.ai_trading.database.models import (
    Base, User, MT5Account, Trade, TradingSignal, AIModel, SystemLog, MarketData
)
from src.ai_trading.utils.logger import get_logger
from config import settings

logger = get_logger(__name__)


class DatabaseManager:
    """Database operations manager"""
    
    def __init__(self):
        self.engine = None
        self.async_session_maker = None
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize database connection and create tables"""
        try:
            logger.info("Initializing database connection...")
            
            # Create async engine
            self.engine = create_async_engine(
                settings.database_url,
                echo=settings.debug,
                pool_pre_ping=True,
                pool_recycle=3600
            )
            
            # Create session maker
            self.async_session_maker = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Create tables
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            self.is_initialized = True
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    @asynccontextmanager
    async def get_session(self):
        """Get database session context manager"""
        if not self.is_initialized:
            raise RuntimeError("Database not initialized")
        
        async with self.async_session_maker() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    # User operations
    async def create_user(self, username: str, email: str, hashed_password: str, 
                         full_name: Optional[str] = None) -> User:
        """Create a new user"""
        async with self.get_session() as session:
            user = User(
                username=username,
                email=email,
                hashed_password=hashed_password,
                full_name=full_name
            )
            session.add(user)
            await session.flush()
            await session.refresh(user)
            return user
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        async with self.get_session() as session:
            result = await session.execute(
                select(User).where(User.email == email)
            )
            return result.scalar_one_or_none()
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        async with self.get_session() as session:
            result = await session.execute(
                select(User).where(User.username == username)
            )
            return result.scalar_one_or_none()
    
    async def get_user(self, user_id: int) -> Optional[User]:
        """Get user by ID"""
        async with self.get_session() as session:
            result = await session.execute(
                select(User).where(User.id == user_id)
            )
            return result.scalar_one_or_none()

    async def update_user(self, user_id: int, **kwargs):
        """Update user information"""
        async with self.get_session() as session:
            result = await session.execute(
                select(User).where(User.id == user_id)
            )
            user = result.scalar_one_or_none()
            if user:
                for key, value in kwargs.items():
                    if hasattr(user, key) and value is not None:
                        setattr(user, key, value)
                user.updated_at = datetime.now()

    async def update_user_password(self, user_id: int, hashed_password: str):
        """Update user password"""
        async with self.get_session() as session:
            result = await session.execute(
                select(User).where(User.id == user_id)
            )
            user = result.scalar_one_or_none()
            if user:
                user.hashed_password = hashed_password
                user.updated_at = datetime.now()

    # MT5 Account operations
    async def create_mt5_account(self, user_id: int, login: str, server: str,
                               encrypted_password: str, account_name: str,
                               metaapi_account_id: str) -> MT5Account:
        """Create a new MT5 account"""
        async with self.get_session() as session:
            account = MT5Account(
                user_id=user_id,
                login=login,
                server=server,
                encrypted_password=encrypted_password,
                account_name=account_name,
                metaapi_account_id=metaapi_account_id
            )
            session.add(account)
            await session.flush()
            await session.refresh(account)
            return account
    
    async def get_mt5_account(self, account_id: int) -> Optional[MT5Account]:
        """Get MT5 account by ID"""
        async with self.get_session() as session:
            result = await session.execute(
                select(MT5Account).where(MT5Account.id == account_id)
            )
            return result.scalar_one_or_none()
    
    async def get_mt5_account_by_login(self, login: str) -> Optional[MT5Account]:
        """Get MT5 account by login"""
        async with self.get_session() as session:
            result = await session.execute(
                select(MT5Account).where(MT5Account.login == login)
            )
            return result.scalar_one_or_none()
    
    async def get_user_mt5_accounts(self, user_id: int) -> List[MT5Account]:
        """Get all MT5 accounts for a user"""
        async with self.get_session() as session:
            result = await session.execute(
                select(MT5Account).where(MT5Account.user_id == user_id)
                .order_by(MT5Account.created_at.desc())
            )
            return result.scalars().all()
    
    async def get_active_mt5_accounts(self) -> List[MT5Account]:
        """Get all active MT5 accounts"""
        async with self.get_session() as session:
            result = await session.execute(
                select(MT5Account).where(MT5Account.status == 'connected')
            )
            return result.scalars().all()
    
    async def update_account_status(self, account_id: int, status: str):
        """Update MT5 account status"""
        async with self.get_session() as session:
            result = await session.execute(
                select(MT5Account).where(MT5Account.id == account_id)
            )
            account = result.scalar_one_or_none()
            if account:
                account.status = status
                account.updated_at = datetime.now()

    async def update_account_balance(self, account_id: int, balance: float, equity: float = None):
        """Update MT5 account balance and equity"""
        async with self.get_session() as session:
            result = await session.execute(
                select(MT5Account).where(MT5Account.id == account_id)
            )
            account = result.scalar_one_or_none()
            if account:
                account.balance = balance
                if equity is not None:
                    account.equity = equity
                account.updated_at = datetime.now()

    async def delete_mt5_account(self, account_id: int):
        """Delete MT5 account"""
        async with self.get_session() as session:
            result = await session.execute(
                select(MT5Account).where(MT5Account.id == account_id)
            )
            account = result.scalar_one_or_none()
            if account:
                await session.delete(account)

    # Trade operations
    async def create_trade(self, user_id: int, mt5_account_id: int, symbol: str,
                          action: str, volume: float, **kwargs) -> Trade:
        """Create a new trade record"""
        async with self.get_session() as session:
            trade = Trade(
                user_id=user_id,
                mt5_account_id=mt5_account_id,
                symbol=symbol,
                action=action,
                volume=volume,
                **kwargs
            )
            session.add(trade)
            await session.flush()
            await session.refresh(trade)
            return trade
    
    async def update_trade(self, trade_id: int, **kwargs):
        """Update trade record"""
        async with self.get_session() as session:
            result = await session.execute(
                select(Trade).where(Trade.id == trade_id)
            )
            trade = result.scalar_one_or_none()
            if trade:
                for key, value in kwargs.items():
                    setattr(trade, key, value)
                trade.updated_at = datetime.now()
    
    async def get_account_trades(self, account_id: int, limit: int = 100) -> List[Trade]:
        """Get trades for an account"""
        async with self.get_session() as session:
            result = await session.execute(
                select(Trade).where(Trade.mt5_account_id == account_id)
                .order_by(Trade.created_at.desc())
                .limit(limit)
            )
            return result.scalars().all()
    
    async def get_user_trades(self, user_id: int, limit: int = 100) -> List[Trade]:
        """Get trades for a user"""
        async with self.get_session() as session:
            result = await session.execute(
                select(Trade).where(Trade.user_id == user_id)
                .order_by(Trade.created_at.desc())
                .limit(limit)
            )
            return result.scalars().all()

    async def get_recent_trades(self, user_id: int = None, limit: int = 10) -> List[Trade]:
        """Get recent trades, optionally filtered by user"""
        async with self.get_session() as session:
            query = select(Trade)
            if user_id:
                query = query.where(Trade.user_id == user_id)

            result = await session.execute(
                query.order_by(Trade.created_at.desc()).limit(limit)
            )
            return result.scalars().all()

    async def get_trade_statistics(self, user_id: int = None) -> Dict[str, Any]:
        """Get trade statistics for a user or all users"""
        async with self.get_session() as session:
            query_base = select(Trade)
            if user_id:
                query_base = query_base.where(Trade.user_id == user_id)

            # Total trades
            total_result = await session.execute(
                select(func.count(Trade.id)).select_from(query_base.subquery())
            )
            total_trades = total_result.scalar() or 0

            # Closed trades only for profit calculations
            closed_query = query_base.where(Trade.status == 'closed')

            # Total profit
            profit_result = await session.execute(
                select(func.sum(Trade.profit)).select_from(closed_query.subquery())
            )
            total_profit = profit_result.scalar() or 0.0

            # Winning trades
            winning_result = await session.execute(
                select(func.count(Trade.id)).select_from(
                    closed_query.where(Trade.profit > 0).subquery()
                )
            )
            winning_trades = winning_result.scalar() or 0

            # Closed trades count
            closed_result = await session.execute(
                select(func.count(Trade.id)).select_from(closed_query.subquery())
            )
            closed_trades = closed_result.scalar() or 0

            # Calculate win rate
            win_rate = (winning_trades / closed_trades * 100) if closed_trades > 0 else 0.0

            return {
                'total_trades': total_trades,
                'closed_trades': closed_trades,
                'winning_trades': winning_trades,
                'total_profit': total_profit,
                'win_rate': win_rate
            }

    # Trading Signal operations
    async def create_trading_signal(self, symbol: str, action: str, confidence: float,
                                  **kwargs) -> TradingSignal:
        """Create a new trading signal"""
        async with self.get_session() as session:
            signal = TradingSignal(
                symbol=symbol,
                action=action,
                confidence=confidence,
                **kwargs
            )
            session.add(signal)
            await session.flush()
            await session.refresh(signal)
            return signal
    
    async def get_active_signals(self, symbol: Optional[str] = None) -> List[TradingSignal]:
        """Get active trading signals"""
        async with self.get_session() as session:
            query = select(TradingSignal).where(TradingSignal.status == 'active')
            if symbol:
                query = query.where(TradingSignal.symbol == symbol)
            
            result = await session.execute(
                query.order_by(TradingSignal.generated_at.desc())
            )
            return result.scalars().all()

    async def get_recent_signals(self, limit: int = 10) -> List[TradingSignal]:
        """Get recent trading signals"""
        async with self.get_session() as session:
            result = await session.execute(
                select(TradingSignal)
                .order_by(TradingSignal.generated_at.desc())
                .limit(limit)
            )
            return result.scalars().all()

    async def update_signal_status(self, signal_id: int, status: str):
        """Update trading signal status"""
        async with self.get_session() as session:
            result = await session.execute(
                select(TradingSignal).where(TradingSignal.id == signal_id)
            )
            signal = result.scalar_one_or_none()
            if signal:
                signal.status = status
                signal.updated_at = datetime.now()

    # Statistics operations
    async def get_total_users(self) -> int:
        """Get total number of users"""
        async with self.get_session() as session:
            result = await session.execute(select(func.count(User.id)))
            return result.scalar()
    
    async def get_total_mt5_accounts(self) -> int:
        """Get total number of MT5 accounts"""
        async with self.get_session() as session:
            result = await session.execute(select(func.count(MT5Account.id)))
            return result.scalar()
    
    async def get_active_mt5_accounts_count(self) -> int:
        """Get number of active MT5 accounts"""
        async with self.get_session() as session:
            result = await session.execute(
                select(func.count(MT5Account.id))
                .where(MT5Account.status == 'connected')
            )
            return result.scalar()
    
    async def get_total_trades(self) -> int:
        """Get total number of trades"""
        async with self.get_session() as session:
            result = await session.execute(select(func.count(Trade.id)))
            return result.scalar()
    
    # System logging
    async def log_system_event(self, level: str, message: str, module: str = None,
                             function: str = None, **kwargs):
        """Log system event"""
        async with self.get_session() as session:
            log_entry = SystemLog(
                level=level,
                message=message,
                module=module,
                function=function,
                **kwargs
            )
            session.add(log_entry)
    
    # Market data operations
    async def store_market_data(self, symbol: str, timeframe: str, 
                              market_data: List[Dict[str, Any]]):
        """Store market data"""
        async with self.get_session() as session:
            for data in market_data:
                market_record = MarketData(
                    symbol=symbol,
                    timeframe=timeframe,
                    **data
                )
                session.add(market_record)
    
    async def get_market_data(self, symbol: str, timeframe: str, 
                            start_time: datetime, end_time: datetime) -> List[MarketData]:
        """Get market data for a symbol and timeframe"""
        async with self.get_session() as session:
            result = await session.execute(
                select(MarketData)
                .where(
                    and_(
                        MarketData.symbol == symbol,
                        MarketData.timeframe == timeframe,
                        MarketData.timestamp >= start_time,
                        MarketData.timestamp <= end_time
                    )
                )
                .order_by(MarketData.timestamp)
            )
            return result.scalars().all()
    
    async def close(self):
        """Close database connections"""
        if self.engine:
            await self.engine.dispose()
            logger.info("Database connections closed")
