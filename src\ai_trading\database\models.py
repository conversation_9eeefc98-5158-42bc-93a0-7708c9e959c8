"""
Database models for the AI Forex Trading Platform
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

Base = declarative_base()


class User(Base):
    """User model"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    mt5_accounts = relationship("MT5Account", back_populates="user")
    trades = relationship("Trade", back_populates="user")


class MT5Account(Base):
    """MT5 Account model"""
    __tablename__ = "mt5_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    login = Column(String(50), unique=True, index=True, nullable=False)
    server = Column(String(100), nullable=False)
    account_name = Column(String(100))
    encrypted_password = Column(Text, nullable=False)  # Encrypted password
    metaapi_account_id = Column(String(100), unique=True)  # MetaApi account ID
    status = Column(String(20), default="connected")  # connected, disconnected, error
    last_balance = Column(Float, default=0.0)
    last_equity = Column(Float, default=0.0)
    currency = Column(String(10), default="USD")
    leverage = Column(Integer, default=100)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="mt5_accounts")
    trades = relationship("Trade", back_populates="mt5_account")


class Trade(Base):
    """Trade model"""
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    mt5_account_id = Column(Integer, ForeignKey("mt5_accounts.id"), nullable=False)
    
    # Trade details
    symbol = Column(String(20), nullable=False)
    action = Column(String(10), nullable=False)  # buy, sell
    volume = Column(Float, nullable=False)
    open_price = Column(Float)
    close_price = Column(Float)
    stop_loss = Column(Float)
    take_profit = Column(Float)
    
    # Trade results
    profit = Column(Float)
    commission = Column(Float, default=0.0)
    swap = Column(Float, default=0.0)
    
    # Trade status
    status = Column(String(20), default="open")  # open, closed, cancelled
    
    # MetaApi IDs
    metaapi_position_id = Column(String(100))
    metaapi_order_id = Column(String(100))
    
    # Timestamps
    open_time = Column(DateTime(timezone=True))
    close_time = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # AI-related fields
    signal_id = Column(Integer, ForeignKey("trading_signals.id"))
    ai_confidence = Column(Float)  # AI model confidence score
    volatility_filter_passed = Column(Boolean, default=True)
    
    # Trade metadata
    comment = Column(Text)
    tags = Column(Text)  # JSON string of tags
    
    # Relationships
    user = relationship("User", back_populates="trades")
    mt5_account = relationship("MT5Account", back_populates="trades")
    signal = relationship("TradingSignal", back_populates="trades")


class TradingSignal(Base):
    """Trading Signal model"""
    __tablename__ = "trading_signals"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Signal details
    symbol = Column(String(20), nullable=False)
    action = Column(String(10), nullable=False)  # buy, sell
    confidence = Column(Float, nullable=False)  # AI confidence score (0-1)
    
    # Price levels
    entry_price = Column(Float)
    stop_loss = Column(Float)
    take_profit = Column(Float)
    
    # Signal source
    model_name = Column(String(100))  # AI model that generated the signal
    model_version = Column(String(20))
    
    # Market conditions
    volatility_score = Column(Float)  # Volatility filter score
    market_conditions = Column(Text)  # JSON string of market conditions
    
    # Signal status
    status = Column(String(20), default="active")  # active, executed, expired, cancelled
    
    # Timestamps
    generated_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True))
    executed_at = Column(DateTime(timezone=True))
    
    # Performance tracking
    trades_count = Column(Integer, default=0)
    success_rate = Column(Float)
    average_profit = Column(Float)
    
    # Relationships
    trades = relationship("Trade", back_populates="signal")


class AIModel(Base):
    """AI Model tracking"""
    __tablename__ = "ai_models"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Model details
    name = Column(String(100), nullable=False)
    version = Column(String(20), nullable=False)
    model_type = Column(String(50))  # volatility_filter, signal_generator, etc.
    file_path = Column(String(255))  # Path to saved model file
    
    # Model performance
    accuracy = Column(Float)
    precision = Column(Float)
    recall = Column(Float)
    f1_score = Column(Float)
    
    # Training details
    training_samples = Column(Integer)
    training_features = Column(Integer)
    training_duration = Column(Float)  # Training time in seconds
    
    # Model status
    is_active = Column(Boolean, default=True)
    is_production = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    trained_at = Column(DateTime(timezone=True))
    deployed_at = Column(DateTime(timezone=True))
    
    # Model configuration
    hyperparameters = Column(Text)  # JSON string of hyperparameters
    feature_names = Column(Text)  # JSON string of feature names
    
    # Performance tracking
    predictions_count = Column(Integer, default=0)
    correct_predictions = Column(Integer, default=0)
    live_accuracy = Column(Float)


class SystemLog(Base):
    """System logging"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Log details
    level = Column(String(20), nullable=False)  # INFO, WARNING, ERROR, DEBUG
    message = Column(Text, nullable=False)
    module = Column(String(100))  # Module that generated the log
    function = Column(String(100))  # Function that generated the log
    
    # Context
    user_id = Column(Integer, ForeignKey("users.id"))
    trade_id = Column(Integer, ForeignKey("trades.id"))
    account_id = Column(Integer, ForeignKey("mt5_accounts.id"))
    
    # Additional data
    extra_data = Column(Text)  # JSON string of additional data
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")
    trade = relationship("Trade")
    account = relationship("MT5Account")


class MarketData(Base):
    """Market data storage"""
    __tablename__ = "market_data"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Market data details
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)  # M1, M5, M15, etc.
    
    # OHLCV data
    timestamp = Column(DateTime(timezone=True), nullable=False)
    open_price = Column(Float, nullable=False)
    high_price = Column(Float, nullable=False)
    low_price = Column(Float, nullable=False)
    close_price = Column(Float, nullable=False)
    volume = Column(Float, default=0.0)
    
    # Additional data
    spread = Column(Float)
    tick_volume = Column(Integer)
    
    # Metadata
    source = Column(String(50))  # Data source (MetaApi, etc.)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Indexes for efficient querying
    __table_args__ = (
        {'mysql_engine': 'InnoDB'},
    )


# Create indexes for better performance
from sqlalchemy import Index

# Composite indexes for better query performance
Index('idx_user_email_active', User.email, User.is_active)
Index('idx_mt5_user_status', MT5Account.user_id, MT5Account.status)
Index('idx_trade_account_status', Trade.mt5_account_id, Trade.status)
Index('idx_trade_symbol_time', Trade.symbol, Trade.open_time)
Index('idx_signal_symbol_status', TradingSignal.symbol, TradingSignal.status)
Index('idx_market_symbol_time', MarketData.symbol, MarketData.timestamp)
