#!/usr/bin/env python3
"""
Startup script for the AI Forex Trading Platform Web Server
"""
import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import asyncpg
        print("✓ All required packages are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing required package: {e}")
        print("Please install requirements with: pip install -r requirements-web.txt")
        return False

def check_environment():
    """Check if environment is properly configured"""
    env_file = Path(".env")
    if not env_file.exists():
        print("✗ .env file not found")
        print("Please create a .env file with your configuration")
        print("Example:")
        print("DATABASE_URL=postgresql+asyncpg://user:password@localhost/forex_trading")
        print("SECRET_KEY=your-secret-key-here")
        print("METAAPI_TOKEN=your-metaapi-token")
        return False
    
    print("✓ Environment file found")
    return True

def create_directories():
    """Create required directories"""
    directories = ["logs", "data", "models", "static", "templates"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✓ Required directories created")

def main():
    """Main startup function"""
    print("🚀 Starting AI Forex Trading Platform Web Server")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    print("\n🌐 Starting web server...")
    print("Dashboard will be available at: http://localhost:8000")
    print("API documentation at: http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Start the server
        os.system("python main.py")
    except KeyboardInterrupt:
        print("\n👋 Server stopped")

if __name__ == "__main__":
    main()
