# AI-Powered Forex Autotrading Platform

A comprehensive AI-driven Forex trading platform that uses MetaApi to connect and trade across multiple MT5 accounts via ThinkMarkets. The platform features intelligent volatility filtering and automated trade copying to scale across hundreds of users.

## 🚀 Features

- **Central AI Trading Engine**: Advanced ML models for signal generation
- **Intelligent Volatility Filter**: AI-powered market condition assessment
- **Multi-Account Trading**: Automated trade copying via MetaApi
- **ThinkMarkets Integration**: Optimized for ThinkMarkets MT5 accounts
- **Scalable Architecture**: Supports 100+ connected user accounts
- **Real-time Dashboard**: Web interface for monitoring and management
- **Risk Management**: Advanced position sizing and drawdown protection

## 🏗️ Architecture

```
├── src/ai_trading/
│   ├── core/           # Core trading engine and logic
│   ├── models/         # AI models and volatility filter
│   ├── api/            # Web API and dashboard
│   ├── database/       # Database models and operations
│   └── utils/          # Utilities and helpers
├── data/               # Training data and market data
├── models/             # Saved ML models
├── logs/               # Application logs
└── static/             # Web assets
```

## 🛠️ Technology Stack

- **Backend**: Python 3.10+, FastAPI, SQLAlchemy
- **AI/ML**: scikit-learn, Tensor<PERSON>low, PyTorch, XGBoost
- **Trading**: MetaApi SDK, ThinkMarkets MT5
- **Database**: PostgreSQL
- **Frontend**: HTML/CSS/JavaScript (Dashboard)
- **Deployment**: Docker, AWS/DigitalOcean

## 📋 Prerequisites

1. **ThinkMarkets MT5 Account** (Demo or Live)
2. **MetaApi Account** with API token
3. **Python 3.10+**
4. **PostgreSQL Database**

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd forex-ai-trading
pip install -r requirements.txt
```

### 2. Configuration

```bash
cp .env.example .env
# Edit .env with your credentials
```

### 3. Database Setup

```bash
# Create PostgreSQL database
createdb forex_trading_db

# Run migrations
python -m alembic upgrade head
```

### 4. Run the Platform

```bash
python main.py
```

The platform will start:
- Trading engine running in background
- Web dashboard at http://localhost:8000
- API endpoints available

## 🔧 Configuration

Key configuration options in `.env`:

```env
# MetaApi Settings
METAAPI_TOKEN=your_token_here
MASTER_ACCOUNT_ID=your_master_account_id

# Trading Parameters
DEFAULT_RISK_PERCENT=1.0
VOLATILITY_THRESHOLD=0.7
MAX_CONCURRENT_TRADES=5

# Database
DATABASE_URL=postgresql://user:pass@localhost/forex_trading_db
```

## 🤖 AI Models

### Volatility Filter Features
- ATR (Average True Range)
- Bollinger Band Width
- Standard Deviation
- Volume Analysis
- RSI Levels
- Time-based Filters

### Signal Generation
- Multi-timeframe analysis
- Technical indicator fusion
- Market regime detection
- Risk-adjusted position sizing

## 📊 Usage

### For Platform Operators

1. Configure master ThinkMarkets account
2. Set up MetaApi provider role
3. Train AI models with historical data
4. Monitor performance via dashboard

### For End Users

1. Sign up on platform
2. Connect MT5 account credentials
3. Accept automated trading terms
4. Monitor trades via dashboard

## 🔒 Security

- Encrypted credential storage
- Secure MetaApi integration
- User account isolation
- Audit logging
- Rate limiting

## 📈 Scaling

The platform is designed to scale:
- Horizontal scaling with load balancers
- Database connection pooling
- Async processing for trade execution
- Microservices architecture ready

## 🧪 Testing

```bash
# Run unit tests
pytest src/tests/

# Run integration tests
pytest src/tests/integration/

# Test with demo accounts
python scripts/test_demo_trading.py
```

## 📝 API Documentation

Once running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🚀 Deployment

### Docker Deployment

```bash
docker build -t forex-ai-trading .
docker run -p 8000:8000 forex-ai-trading
```

### Production Checklist

- [ ] Set `ENVIRONMENT=production`
- [ ] Use strong `SECRET_KEY`
- [ ] Configure SSL/TLS
- [ ] Set up monitoring
- [ ] Configure backups
- [ ] Test failover procedures

## 📞 Support

For issues and questions:
- Check the documentation
- Review logs in `logs/` directory
- Test with demo accounts first
- Ensure MetaApi connectivity

## ⚠️ Disclaimer

This software is for educational and research purposes. Trading involves substantial risk of loss. Past performance does not guarantee future results. Use at your own risk.

## 📄 License

[Add your license here]
