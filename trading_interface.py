#!/usr/bin/env python3
"""
Interactive Trading Interface for AI Forex Platform
"""
import asyncio
import os
from dotenv import load_dotenv
from metaapi_cloud_sdk import MetaApi

# Load environment variables
load_dotenv('.env', override=True)

class TradingInterface:
    def __init__(self):
        self.api = None
        self.account = None
        self.connection = None
        
    async def initialize(self):
        """Initialize MetaApi connection"""
        token = os.getenv('METAAPI_TOKEN')
        account_id = os.getenv('MASTER_ACCOUNT_ID')
        
        print("🔌 Initializing Trading Interface...")
        self.api = MetaApi(token)
        
        # Get account
        accounts = await self.api.metatrader_account_api.get_accounts_with_infinite_scroll_pagination()
        for account in accounts:
            if account.login == account_id:
                self.account = account
                break
        
        if not self.account:
            raise Exception(f"Account {account_id} not found")
            
        # Connect
        self.connection = self.account.get_streaming_connection()
        await self.connection.connect()
        await asyncio.sleep(3)  # Wait for sync
        
        print("✅ Trading interface ready!")
        
    async def get_account_info(self):
        """Get account information"""
        try:
            terminal_state = self.connection.terminal_state
            if terminal_state and hasattr(terminal_state, 'account_information'):
                account_info = terminal_state.account_information
                if account_info:
                    return {
                        'balance': account_info.get('balance', 0),
                        'equity': account_info.get('equity', 0),
                        'currency': account_info.get('currency', 'USD'),
                        'leverage': account_info.get('leverage', 100),
                        'margin': account_info.get('margin', 0),
                        'free_margin': account_info.get('freeMargin', 0)
                    }
            return None
        except Exception as e:
            print(f"Error getting account info: {e}")
            return None
    
    async def get_positions(self):
        """Get current positions"""
        try:
            terminal_state = self.connection.terminal_state
            if terminal_state and hasattr(terminal_state, 'positions'):
                return terminal_state.positions
            return []
        except Exception as e:
            print(f"Error getting positions: {e}")
            return []
    
    async def get_symbols(self):
        """Get available symbols"""
        try:
            terminal_state = self.connection.terminal_state
            if terminal_state and hasattr(terminal_state, 'symbols'):
                symbols = terminal_state.symbols
                # Filter major pairs
                major_pairs = []
                for symbol_name, symbol_data in symbols.items():
                    if symbol_name in ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']:
                        major_pairs.append({
                            'symbol': symbol_name,
                            'bid': symbol_data.get('bid', 0),
                            'ask': symbol_data.get('ask', 0),
                            'spread': symbol_data.get('ask', 0) - symbol_data.get('bid', 0)
                        })
                return major_pairs
            return []
        except Exception as e:
            print(f"Error getting symbols: {e}")
            return []
    
    async def place_trade(self, symbol, action, volume, stop_loss=None, take_profit=None):
        """Place a trade"""
        try:
            trade_request = {
                'actionType': 'ORDER_TYPE_BUY' if action.upper() == 'BUY' else 'ORDER_TYPE_SELL',
                'symbol': symbol,
                'volume': volume
            }
            
            if stop_loss:
                trade_request['stopLoss'] = stop_loss
            if take_profit:
                trade_request['takeProfit'] = take_profit
                
            result = await self.connection.create_market_buy_order(symbol, volume, stop_loss, take_profit) if action.upper() == 'BUY' else await self.connection.create_market_sell_order(symbol, volume, stop_loss, take_profit)
            
            return result
        except Exception as e:
            print(f"Error placing trade: {e}")
            return None
    
    async def close(self):
        """Close connection"""
        if self.connection:
            await self.connection.close()

async def main():
    """Main trading interface"""
    interface = TradingInterface()
    
    try:
        await interface.initialize()
        
        while True:
            print("\n" + "="*50)
            print("🚀 AI FOREX TRADING PLATFORM")
            print("="*50)
            
            # Show account info
            account_info = await interface.get_account_info()
            if account_info:
                print(f"💰 Balance: ${account_info['balance']:,.2f}")
                print(f"📊 Equity: ${account_info['equity']:,.2f}")
                print(f"💱 Currency: {account_info['currency']}")
                print(f"🔢 Leverage: 1:{account_info['leverage']}")
                print(f"📈 Free Margin: ${account_info['free_margin']:,.2f}")
            
            # Show positions
            positions = await interface.get_positions()
            print(f"\n📈 Open Positions: {len(positions)}")
            for pos in positions:
                print(f"   {pos.get('symbol', 'N/A')}: {pos.get('type', 'N/A')} {pos.get('volume', 0)} lots")
            
            # Show major pairs
            symbols = await interface.get_symbols()
            if symbols:
                print(f"\n💱 Major Pairs:")
                for symbol in symbols[:5]:
                    spread = symbol['spread']
                    print(f"   {symbol['symbol']}: Bid {symbol['bid']:.5f} | Ask {symbol['ask']:.5f} | Spread {spread:.5f}")
            
            print(f"\n🎯 Trading Options:")
            print("1. 📈 Place BUY order")
            print("2. 📉 Place SELL order") 
            print("3. 🔄 Refresh data")
            print("4. 🚪 Exit")
            
            choice = input("\nSelect option (1-4): ").strip()
            
            if choice == '1' or choice == '2':
                action = 'BUY' if choice == '1' else 'SELL'
                symbol = input("Enter symbol (e.g., EURUSD): ").strip().upper()
                try:
                    volume = float(input("Enter volume (e.g., 0.01): ").strip())
                    
                    # Optional SL/TP
                    sl_input = input("Stop Loss (optional, press Enter to skip): ").strip()
                    tp_input = input("Take Profit (optional, press Enter to skip): ").strip()
                    
                    stop_loss = float(sl_input) if sl_input else None
                    take_profit = float(tp_input) if tp_input else None
                    
                    print(f"\n🔄 Placing {action} order for {volume} lots of {symbol}...")
                    result = await interface.place_trade(symbol, action, volume, stop_loss, take_profit)
                    
                    if result:
                        print(f"✅ Trade placed successfully!")
                        print(f"Order ID: {result.get('orderId', 'N/A')}")
                    else:
                        print("❌ Failed to place trade")
                        
                except ValueError:
                    print("❌ Invalid input. Please enter valid numbers.")
                except Exception as e:
                    print(f"❌ Error: {e}")
                    
            elif choice == '3':
                print("🔄 Refreshing data...")
                continue
                
            elif choice == '4':
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid option. Please select 1-4.")
                
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await interface.close()

if __name__ == "__main__":
    asyncio.run(main())
