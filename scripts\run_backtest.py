#!/usr/bin/env python3
"""
Backtesting script for the AI trading strategy
"""
import sys
import os
import asyncio
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ai_trading.backtesting.backtester import Backtester, BacktestConfig
from src.ai_trading.models.signal_generator import TrainingDataGenerator
from src.ai_trading.utils.logger import get_logger

logger = get_logger(__name__)


def generate_realistic_market_data(symbol: str, days: int = 365) -> pd.DataFrame:
    """Generate realistic market data for backtesting"""
    try:
        logger.info(f"Generating {days} days of market data for {symbol}")
        
        # Base parameters for different symbols
        symbol_params = {
            'XAUUSD': {'base_price': 1800, 'volatility': 0.015, 'trend': 0.0001},
            'EURUSD': {'base_price': 1.1000, 'volatility': 0.008, 'trend': -0.00005},
            'GBPUSD': {'base_price': 1.2500, 'volatility': 0.010, 'trend': 0.00002},
            'USDJPY': {'base_price': 110.00, 'volatility': 0.007, 'trend': 0.00003},
        }
        
        params = symbol_params.get(symbol, symbol_params['XAUUSD'])
        
        # Generate time series
        start_date = datetime.now() - timedelta(days=days)
        dates = pd.date_range(start=start_date, periods=days*24*12, freq='5T')  # 5-minute bars
        
        # Generate price series using geometric Brownian motion
        n_points = len(dates)
        dt = 1/288  # 5 minutes as fraction of day
        
        # Random walk with trend and volatility
        returns = np.random.normal(
            params['trend'] * dt,
            params['volatility'] * np.sqrt(dt),
            n_points
        )
        
        # Add some market regime changes
        regime_changes = np.random.choice(n_points, size=n_points//100, replace=False)
        for change_point in regime_changes:
            # Increase volatility for a period
            end_point = min(change_point + 100, n_points)
            returns[change_point:end_point] *= np.random.uniform(1.5, 3.0)
        
        # Calculate prices
        log_prices = np.log(params['base_price']) + np.cumsum(returns)
        prices = np.exp(log_prices)
        
        # Generate OHLCV data
        data = []
        for i in range(0, len(prices), 12):  # Group into 1-hour bars
            if i + 12 > len(prices):
                break
                
            hour_prices = prices[i:i+12]
            
            open_price = hour_prices[0]
            close_price = hour_prices[-1]
            high_price = np.max(hour_prices)
            low_price = np.min(hour_prices)
            
            # Generate volume (higher volume during volatility)
            volatility = np.std(hour_prices) / np.mean(hour_prices)
            base_volume = np.random.uniform(1000, 5000)
            volume = base_volume * (1 + volatility * 10)
            
            data.append({
                'timestamp': dates[i],
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        logger.info(f"Generated {len(df)} data points for {symbol}")
        logger.info(f"Price range: {df['low'].min():.4f} - {df['high'].max():.4f}")
        
        return df
        
    except Exception as e:
        logger.error(f"Error generating market data for {symbol}: {e}")
        return pd.DataFrame()


async def run_backtest():
    """Run comprehensive backtest"""
    try:
        logger.info("🚀 Starting AI Trading Strategy Backtest")
        
        # Configure backtest
        config = BacktestConfig(
            initial_balance=10000.0,
            risk_percent=2.0,
            min_confidence=0.6,
            max_positions=3,
            commission=0.0001,
            spread=0.0002,
            slippage=0.0001
        )
        
        logger.info("📊 Backtest Configuration:")
        logger.info(f"  Initial Balance: ${config.initial_balance:,.2f}")
        logger.info(f"  Risk per Trade: {config.risk_percent}%")
        logger.info(f"  Min Confidence: {config.min_confidence}")
        logger.info(f"  Max Positions: {config.max_positions}")
        logger.info(f"  Commission: {config.commission*100:.3f}%")
        
        # Generate market data for multiple symbols
        symbols = ['XAUUSD', 'EURUSD', 'GBPUSD']
        market_data = {}
        
        for symbol in symbols:
            market_data[symbol] = generate_realistic_market_data(symbol, days=180)  # 6 months
        
        # Initialize backtester
        backtester = Backtester(config)
        
        # Set backtest period
        start_date = datetime.now() - timedelta(days=150)
        end_date = datetime.now() - timedelta(days=30)
        
        logger.info(f"📅 Backtest Period: {start_date.date()} to {end_date.date()}")
        
        # Run backtest
        results = backtester.run_backtest(
            market_data=market_data,
            start_date=start_date,
            end_date=end_date
        )
        
        # Display results
        display_results(results)
        
        # Plot results
        try:
            plot_path = "backtest_results.png"
            backtester.plot_results(results, save_path=plot_path)
        except Exception as e:
            logger.warning(f"Could not generate plots: {e}")
        
        # Analyze trades
        analyze_trades(results.get('trades', []))
        
        # Performance summary
        display_performance_summary(results)
        
        return results
        
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        raise


def display_results(results: dict):
    """Display backtest results"""
    logger.info("=" * 60)
    logger.info("📈 BACKTEST RESULTS")
    logger.info("=" * 60)
    
    logger.info(f"💰 Financial Performance:")
    logger.info(f"  Initial Balance:    ${results.get('initial_balance', 0):>10,.2f}")
    logger.info(f"  Final Equity:       ${results.get('final_equity', 0):>10,.2f}")
    logger.info(f"  Total Return:       {results.get('total_return', 0):>10.2f}%")
    logger.info(f"  Annual Return:      {results.get('annual_return', 0):>10.2f}%")
    logger.info(f"  Max Drawdown:       {results.get('max_drawdown', 0):>10.2f}%")
    
    logger.info(f"")
    logger.info(f"📊 Trading Statistics:")
    logger.info(f"  Total Trades:       {results.get('total_trades', 0):>10}")
    logger.info(f"  Winning Trades:     {results.get('winning_trades', 0):>10}")
    logger.info(f"  Losing Trades:      {results.get('losing_trades', 0):>10}")
    logger.info(f"  Win Rate:           {results.get('win_rate', 0):>10.2f}%")
    logger.info(f"  Profit Factor:      {results.get('profit_factor', 0):>10.2f}")
    
    logger.info(f"")
    logger.info(f"💵 P&L Analysis:")
    logger.info(f"  Average Win:        ${results.get('avg_win', 0):>10.2f}")
    logger.info(f"  Average Loss:       ${results.get('avg_loss', 0):>10.2f}")
    
    # Risk-adjusted metrics
    if results.get('total_return', 0) != 0 and results.get('max_drawdown', 0) != 0:
        calmar_ratio = results['total_return'] / results['max_drawdown']
        logger.info(f"  Calmar Ratio:       {calmar_ratio:>10.2f}")


def analyze_trades(trades: list):
    """Analyze individual trades"""
    if not trades:
        logger.info("No trades to analyze")
        return
    
    logger.info("=" * 60)
    logger.info("🔍 TRADE ANALYSIS")
    logger.info("=" * 60)
    
    # Group by symbol
    symbol_stats = {}
    for trade in trades:
        symbol = trade.symbol
        if symbol not in symbol_stats:
            symbol_stats[symbol] = {'trades': [], 'wins': 0, 'losses': 0, 'total_pnl': 0}
        
        symbol_stats[symbol]['trades'].append(trade)
        symbol_stats[symbol]['total_pnl'] += trade.pnl or 0
        
        if trade.pnl and trade.pnl > 0:
            symbol_stats[symbol]['wins'] += 1
        else:
            symbol_stats[symbol]['losses'] += 1
    
    # Display per-symbol statistics
    for symbol, stats in symbol_stats.items():
        total_trades = len(stats['trades'])
        win_rate = (stats['wins'] / total_trades) * 100 if total_trades > 0 else 0
        
        logger.info(f"📊 {symbol}:")
        logger.info(f"  Trades: {total_trades}, Wins: {stats['wins']}, Losses: {stats['losses']}")
        logger.info(f"  Win Rate: {win_rate:.1f}%, Total P&L: ${stats['total_pnl']:.2f}")
    
    # Action analysis
    buy_trades = [t for t in trades if t.action == 'buy']
    sell_trades = [t for t in trades if t.action == 'sell']
    
    logger.info(f"")
    logger.info(f"📈 Action Analysis:")
    logger.info(f"  Buy Trades: {len(buy_trades)}")
    logger.info(f"  Sell Trades: {len(sell_trades)}")
    
    if buy_trades:
        buy_pnl = sum(t.pnl for t in buy_trades if t.pnl)
        buy_wins = len([t for t in buy_trades if t.pnl and t.pnl > 0])
        buy_win_rate = (buy_wins / len(buy_trades)) * 100
        logger.info(f"  Buy Win Rate: {buy_win_rate:.1f}%, Total P&L: ${buy_pnl:.2f}")
    
    if sell_trades:
        sell_pnl = sum(t.pnl for t in sell_trades if t.pnl)
        sell_wins = len([t for t in sell_trades if t.pnl and t.pnl > 0])
        sell_win_rate = (sell_wins / len(sell_trades)) * 100
        logger.info(f"  Sell Win Rate: {sell_win_rate:.1f}%, Total P&L: ${sell_pnl:.2f}")
    
    # Best and worst trades
    if trades:
        best_trade = max(trades, key=lambda t: t.pnl or 0)
        worst_trade = min(trades, key=lambda t: t.pnl or 0)
        
        logger.info(f"")
        logger.info(f"🏆 Best Trade: {best_trade.symbol} {best_trade.action} - ${best_trade.pnl:.2f}")
        logger.info(f"💸 Worst Trade: {worst_trade.symbol} {worst_trade.action} - ${worst_trade.pnl:.2f}")


def display_performance_summary(results: dict):
    """Display performance summary and recommendations"""
    logger.info("=" * 60)
    logger.info("🎯 PERFORMANCE SUMMARY")
    logger.info("=" * 60)
    
    total_return = results.get('total_return', 0)
    win_rate = results.get('win_rate', 0)
    max_drawdown = results.get('max_drawdown', 0)
    profit_factor = results.get('profit_factor', 0)
    
    # Performance rating
    score = 0
    if total_return > 10: score += 1
    if total_return > 20: score += 1
    if win_rate > 50: score += 1
    if win_rate > 60: score += 1
    if max_drawdown < 20: score += 1
    if max_drawdown < 10: score += 1
    if profit_factor > 1.2: score += 1
    if profit_factor > 1.5: score += 1
    
    performance_levels = {
        0: "❌ Poor", 1: "❌ Poor", 2: "⚠️ Below Average",
        3: "⚠️ Below Average", 4: "✅ Average", 5: "✅ Good",
        6: "🚀 Very Good", 7: "🚀 Excellent", 8: "🏆 Outstanding"
    }
    
    performance = performance_levels.get(score, "❓ Unknown")
    
    logger.info(f"📊 Overall Performance: {performance} (Score: {score}/8)")
    logger.info("")
    
    # Recommendations
    logger.info("💡 Recommendations:")
    
    if total_return < 10:
        logger.info("  • Consider adjusting signal confidence threshold")
        logger.info("  • Review feature engineering and model training")
    
    if win_rate < 50:
        logger.info("  • Improve signal quality - current win rate is below 50%")
        logger.info("  • Consider adding more technical indicators")
    
    if max_drawdown > 20:
        logger.info("  • Implement stricter risk management")
        logger.info("  • Consider reducing position sizes")
    
    if profit_factor < 1.2:
        logger.info("  • Focus on improving average win vs average loss ratio")
        logger.info("  • Review stop-loss and take-profit levels")
    
    if results.get('total_trades', 0) < 50:
        logger.info("  • Consider longer backtesting period for more trades")
        logger.info("  • Review signal generation frequency")
    
    logger.info("")
    logger.info("🚀 Next Steps:")
    logger.info("  1. Fine-tune model parameters based on these results")
    logger.info("  2. Test with different market conditions")
    logger.info("  3. Implement paper trading before live deployment")
    logger.info("  4. Monitor performance in real-time")


async def main():
    """Main function"""
    try:
        results = await run_backtest()
        
        logger.info("✅ Backtesting completed successfully!")
        
        return results
        
    except Exception as e:
        logger.error(f"Backtesting failed: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Backtesting interrupted by user")
    except Exception as e:
        logger.error(f"Backtesting script failed: {e}")
        sys.exit(1)
