#!/usr/bin/env python3
"""
AI Trading Engine for Forex Platform
Generates trading signals and executes trades automatically
"""
import asyncio
import os
import random
import numpy as np
from datetime import datetime, timedelta
from dotenv import load_dotenv
from metaapi_cloud_sdk import MetaApi
import logging

# Load environment variables
load_dotenv('.env', override=True)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AITradingEngine:
    def __init__(self):
        self.api = None
        self.account = None
        self.connection = None
        self.is_running = False
        self.positions = {}
        self.signals_history = []
        
        # Trading parameters
        self.symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']
        self.volatility_threshold = 0.7
        self.max_positions = 5
        self.risk_per_trade = 0.02  # 2% risk per trade
        self.min_confidence = 0.65
        
    async def initialize(self):
        """Initialize MetaApi connection"""
        try:
            token = os.getenv('METAAPI_TOKEN')
            account_id = os.getenv('MASTER_ACCOUNT_ID')
            
            if not token or not account_id:
                raise Exception("MetaApi credentials not found")
                
            self.api = MetaApi(token)
            accounts = await self.api.metatrader_account_api.get_accounts_with_infinite_scroll_pagination()
            
            for account in accounts:
                if account.login == account_id:
                    self.account = account
                    break
                    
            if not self.account:
                raise Exception(f"Account {account_id} not found")
                
            self.connection = self.account.get_streaming_connection()
            await self.connection.connect()
            await asyncio.sleep(3)  # Wait for sync
            
            logger.info("✅ AI Trading Engine initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ AI Engine initialization failed: {e}")
            return False
    
    def generate_ai_signal(self, symbol, price_data=None):
        """
        Generate AI trading signal for a symbol
        This is a simplified AI model - in production, use real ML models
        """
        try:
            # Simulate AI analysis with multiple factors
            
            # 1. Trend Analysis (simplified)
            trend_score = random.uniform(-1, 1)
            
            # 2. Momentum Analysis
            momentum_score = random.uniform(-1, 1)
            
            # 3. Volatility Analysis
            volatility = random.uniform(0.3, 1.2)
            
            # 4. Market Sentiment
            sentiment_score = random.uniform(-1, 1)
            
            # 5. Technical Indicators (simulated)
            rsi_signal = random.uniform(-1, 1)
            macd_signal = random.uniform(-1, 1)
            
            # Combine signals with weights
            combined_score = (
                trend_score * 0.3 +
                momentum_score * 0.25 +
                sentiment_score * 0.2 +
                rsi_signal * 0.15 +
                macd_signal * 0.1
            )
            
            # Apply volatility filter
            if volatility > self.volatility_threshold:
                combined_score *= 0.5  # Reduce signal strength in high volatility
            
            # Determine signal
            if combined_score > 0.3:
                signal_type = 'BUY'
                confidence = min(0.95, abs(combined_score) + 0.2)
            elif combined_score < -0.3:
                signal_type = 'SELL'
                confidence = min(0.95, abs(combined_score) + 0.2)
            else:
                signal_type = 'HOLD'
                confidence = 0.5
            
            return {
                'symbol': symbol,
                'signal': signal_type,
                'confidence': round(confidence, 2),
                'volatility': round(volatility, 2),
                'combined_score': round(combined_score, 2),
                'timestamp': datetime.now().isoformat(),
                'factors': {
                    'trend': round(trend_score, 2),
                    'momentum': round(momentum_score, 2),
                    'sentiment': round(sentiment_score, 2),
                    'rsi': round(rsi_signal, 2),
                    'macd': round(macd_signal, 2)
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating signal for {symbol}: {e}")
            return None
    
    async def get_account_info(self):
        """Get current account information"""
        try:
            if self.connection:
                terminal_state = self.connection.terminal_state
                if terminal_state and hasattr(terminal_state, 'account_information'):
                    return terminal_state.account_information
            return None
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return None
    
    async def get_current_positions(self):
        """Get current open positions"""
        try:
            if self.connection:
                terminal_state = self.connection.terminal_state
                if terminal_state and hasattr(terminal_state, 'positions'):
                    return terminal_state.positions
            return []
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    def calculate_position_size(self, account_balance, risk_amount, entry_price, stop_loss):
        """Calculate position size based on risk management"""
        try:
            if not stop_loss or stop_loss == entry_price:
                return 0.01  # Default minimum size
                
            risk_pips = abs(entry_price - stop_loss)
            if risk_pips == 0:
                return 0.01
                
            # Calculate position size based on risk
            pip_value = 10  # Approximate pip value for major pairs
            max_loss = account_balance * risk_amount
            position_size = max_loss / (risk_pips * pip_value)
            
            # Ensure minimum and maximum limits
            position_size = max(0.01, min(position_size, 1.0))
            return round(position_size, 2)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.01
    
    async def execute_trade(self, signal):
        """Execute a trade based on AI signal"""
        try:
            if signal['confidence'] < self.min_confidence:
                logger.info(f"Signal confidence too low: {signal['confidence']}")
                return False
                
            if signal['signal'] == 'HOLD':
                return False
                
            # Check if we already have a position in this symbol
            current_positions = await self.get_current_positions()
            for pos in current_positions:
                if pos.get('symbol') == signal['symbol']:
                    logger.info(f"Already have position in {signal['symbol']}")
                    return False
            
            # Check maximum positions limit
            if len(current_positions) >= self.max_positions:
                logger.info(f"Maximum positions limit reached: {len(current_positions)}")
                return False
            
            # Get account info for position sizing
            account_info = await self.get_account_info()
            if not account_info:
                logger.error("Could not get account information")
                return False
                
            balance = account_info.get('balance', 10000)
            
            # Get current price (simplified)
            current_price = random.uniform(1.0, 1.5)  # In real implementation, get from terminal state
            
            # Calculate stop loss and take profit
            if signal['signal'] == 'BUY':
                stop_loss = current_price * 0.995  # 0.5% stop loss
                take_profit = current_price * 1.015  # 1.5% take profit
            else:
                stop_loss = current_price * 1.005  # 0.5% stop loss
                take_profit = current_price * 0.985  # 1.5% take profit
            
            # Calculate position size
            volume = self.calculate_position_size(balance, self.risk_per_trade, current_price, stop_loss)
            
            # Execute trade
            if signal['signal'] == 'BUY':
                result = await self.connection.create_market_buy_order(
                    signal['symbol'], volume, stop_loss, take_profit
                )
            else:
                result = await self.connection.create_market_sell_order(
                    signal['symbol'], volume, stop_loss, take_profit
                )
            
            if result:
                logger.info(f"✅ Trade executed: {signal['signal']} {volume} lots of {signal['symbol']}")
                logger.info(f"Order ID: {result.get('orderId', 'N/A')}")
                return True
            else:
                logger.error(f"❌ Trade execution failed for {signal['symbol']}")
                return False
                
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return False
    
    async def trading_loop(self):
        """Main AI trading loop"""
        logger.info("🤖 AI Trading Engine started")
        
        while self.is_running:
            try:
                # Generate signals for all symbols
                signals = []
                for symbol in self.symbols:
                    signal = self.generate_ai_signal(symbol)
                    if signal:
                        signals.append(signal)
                        
                # Store signals history
                self.signals_history.extend(signals)
                # Keep only last 100 signals
                self.signals_history = self.signals_history[-100:]
                
                # Execute trades for strong signals
                for signal in signals:
                    if signal['signal'] in ['BUY', 'SELL'] and signal['confidence'] >= self.min_confidence:
                        logger.info(f"🎯 Strong signal: {signal['signal']} {signal['symbol']} (confidence: {signal['confidence']})")
                        await self.execute_trade(signal)
                
                # Wait before next analysis
                await asyncio.sleep(30)  # Analyze every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in trading loop: {e}")
                await asyncio.sleep(10)  # Wait before retrying
    
    async def start(self):
        """Start the AI trading engine"""
        if not await self.initialize():
            return False
            
        self.is_running = True
        await self.trading_loop()
        return True
    
    def stop(self):
        """Stop the AI trading engine"""
        self.is_running = False
        logger.info("⏸️ AI Trading Engine stopped")
    
    def get_latest_signals(self, limit=10):
        """Get latest AI signals"""
        return self.signals_history[-limit:] if self.signals_history else []

# Global AI engine instance
ai_engine = AITradingEngine()

async def main():
    """Main function for standalone execution"""
    try:
        await ai_engine.start()
    except KeyboardInterrupt:
        ai_engine.stop()
        logger.info("AI Trading Engine stopped by user")

if __name__ == "__main__":
    asyncio.run(main())
