#!/usr/bin/env python3
"""
Database setup script for AI Forex Trading Platform
"""
import asyncio
import os
import sys
from pathlib import Path
from sqlalchemy import text
from alembic.config import Config
from alembic import command

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from src.ai_trading.database.database import DatabaseManager
from src.ai_trading.utils.logger import get_logger, setup_logging
from config import settings

setup_logging()
logger = get_logger(__name__)


async def test_connection():
    """Test database connection"""
    try:
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        async with db_manager.get_session() as session:
            result = await session.execute(text("SELECT 1"))
            logger.info("✓ Database connection successful")
            
        await db_manager.close()
        return True
    except Exception as e:
        logger.error(f"✗ Database connection failed: {e}")
        return False


def run_migrations():
    """Run database migrations"""
    try:
        logger.info("Running database migrations...")
        
        # Create alembic config
        alembic_cfg = Config("alembic.ini")
        alembic_cfg.set_main_option("sqlalchemy.url", settings.database_url)
        
        # Run migrations
        command.upgrade(alembic_cfg, "head")
        logger.info("✓ Database migrations completed successfully")
        return True
    except Exception as e:
        logger.error(f"✗ Migration failed: {e}")
        return False


async def create_sample_data():
    """Create sample data for testing"""
    try:
        logger.info("Creating sample data...")
        
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Create a test user
        test_user = await db_manager.create_user(
            username="testuser",
            email="<EMAIL>",
            hashed_password="$2b$12$dummy.hash.for.testing.purposes.only",
            full_name="Test User"
        )
        
        logger.info(f"✓ Created test user: {test_user.email}")
        
        await db_manager.close()
        return True
    except Exception as e:
        logger.error(f"✗ Failed to create sample data: {e}")
        return False


def check_environment():
    """Check if environment is properly configured"""
    logger.info("Checking environment configuration...")
    
    required_vars = [
        "DATABASE_URL",
        "SECRET_KEY",
        "METAAPI_TOKEN"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"✗ Missing environment variables: {', '.join(missing_vars)}")
        logger.info("Please create a .env file with the following variables:")
        logger.info("DATABASE_URL=postgresql+asyncpg://user:password@host:port/database")
        logger.info("SECRET_KEY=your-secret-key-here")
        logger.info("METAAPI_TOKEN=your-metaapi-token")
        return False
    
    logger.info("✓ Environment configuration is valid")
    return True


async def main():
    """Main setup function"""
    print("🚀 AI Forex Trading Platform - Database Setup")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Test connection
    logger.info("Testing database connection...")
    if not await test_connection():
        logger.error("Please check your DATABASE_URL and ensure PostgreSQL is running")
        sys.exit(1)
    
    # Run migrations
    if not run_migrations():
        sys.exit(1)
    
    # Create sample data
    create_sample = input("\nCreate sample test data? (y/N): ").lower().strip()
    if create_sample == 'y':
        await create_sample_data()
    
    print("\n" + "=" * 50)
    print("✅ Database setup completed successfully!")
    print("\nNext steps:")
    print("1. Start the web server: python main.py")
    print("2. Visit http://localhost:8000 to access the dashboard")
    print("3. Register a new account or <NAME_EMAIL>")


if __name__ == "__main__":
    asyncio.run(main())
