version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/forex_trading_db
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_NAME=forex_trading_db
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
    depends_on:
      - db
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./models:/app/models
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=forex_trading_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  postgres_data:
