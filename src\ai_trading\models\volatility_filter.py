"""
AI-powered volatility filter for market condition assessment
"""
import joblib
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix
import xgboost as xgb

from config import settings, TradingConfig
from src.ai_trading.models.feature_extractor import FeatureExtractor
from src.ai_trading.utils.logger import get_logger

logger = get_logger(__name__)


class TrainingDataGenerator:
    """Generate labeled training data for volatility filter"""

    def __init__(self):
        self.feature_extractor = FeatureExtractor()

    def generate_sample_data(self, n_samples: int = 1000) -> pd.DataFrame:
        """
        Generate sample training data for demonstration
        In production, this should be replaced with real market data and manual labels
        """
        logger.info(f"Generating {n_samples} sample training data points")

        # Generate synthetic OHLCV data
        np.random.seed(42)

        training_samples = []

        for i in range(n_samples):
            # Generate synthetic price data
            base_price = 1800 + np.random.normal(0, 100)  # Around gold price
            volatility = np.random.uniform(0.005, 0.05)  # 0.5% to 5% volatility

            # Generate OHLCV for lookback period
            prices = []
            volumes = []

            for j in range(self.feature_extractor.lookback_periods + 1):
                if j == 0:
                    price = base_price
                else:
                    change = np.random.normal(0, volatility * price)
                    price = max(price + change, price * 0.95)  # Prevent negative prices

                # Generate OHLC from close price
                daily_range = price * np.random.uniform(0.001, 0.03)
                high = price + daily_range * np.random.uniform(0, 1)
                low = price - daily_range * np.random.uniform(0, 1)
                open_price = low + (high - low) * np.random.uniform(0, 1)

                prices.append({
                    'timestamp': pd.Timestamp.now() - pd.Timedelta(hours=j),
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': price,
                    'volume': np.random.uniform(1000, 10000)
                })

            # Create DataFrame
            df = pd.DataFrame(prices[::-1])  # Reverse to get chronological order

            try:
                # Extract features
                features = self.feature_extractor.extract_features(df)

                # Generate label based on volatility and other factors
                is_safe = self._generate_label(features, volatility)
                features['is_safe'] = is_safe

                training_samples.append(features)

            except Exception as e:
                logger.warning(f"Error generating sample {i}: {e}")
                continue

        result_df = pd.DataFrame(training_samples)
        logger.info(f"Generated {len(result_df)} training samples")

        return result_df

    def _generate_label(self, features: Dict[str, float], true_volatility: float) -> int:
        """Generate training label based on features and known volatility"""

        # High volatility conditions (risky)
        if (true_volatility > 0.03 or  # High true volatility
            features.get('atr_14', 0) > 50 or  # High ATR
            features.get('bb_width', 0) > 5 or  # Wide Bollinger Bands
            features.get('std_dev_pct', 0) > 3 or  # High standard deviation
            features.get('volume_ratio', 1) > 3 or  # Unusual volume
            features.get('hour_of_day', 12) in [0, 1, 2, 22, 23]):  # Thin liquidity hours
            return 0  # Risky

        # Safe conditions
        return 1


class VolatilityFilter:
    """
    AI-powered volatility filter to assess market conditions
    Determines if market conditions are safe (1) or risky (0) for trading
    """
    
    def __init__(self, model_path: Optional[str] = None):
        self.model = None
        self.scaler = None
        self.feature_extractor = FeatureExtractor()
        self.feature_names = self.feature_extractor.get_feature_names()
        self.model_path = model_path or "models/volatility_filter.pkl"
        self.scaler_path = "models/volatility_scaler.pkl"
        
        # Try to load existing model
        self._load_model()
    
    def predict(self, ohlcv_data: pd.DataFrame, symbol: str = "XAUUSD") -> Tuple[int, float]:
        """
        Predict if market conditions are safe for trading
        
        Args:
            ohlcv_data: OHLCV market data
            symbol: Trading symbol
            
        Returns:
            Tuple of (prediction, confidence)
            prediction: 1 = safe to trade, 0 = risky conditions
            confidence: Probability score (0-1)
        """
        try:
            if self.model is None:
                logger.warning("No trained model available, using default safe prediction")
                return 1, 0.5  # Default to safe with low confidence
            
            # Extract features
            features = self.feature_extractor.extract_features(ohlcv_data, symbol)
            
            # Convert to array in correct order
            feature_array = np.array([features[name] for name in self.feature_names]).reshape(1, -1)
            
            # Scale features if scaler is available
            if self.scaler is not None:
                feature_array = self.scaler.transform(feature_array)
            
            # Make prediction
            prediction = self.model.predict(feature_array)[0]
            
            # Get probability scores
            if hasattr(self.model, 'predict_proba'):
                probabilities = self.model.predict_proba(feature_array)[0]
                confidence = max(probabilities)  # Confidence is the max probability
            else:
                confidence = 0.7  # Default confidence for models without probability
            
            logger.debug(f"Volatility filter prediction: {prediction}, confidence: {confidence:.3f}")
            return int(prediction), float(confidence)
            
        except Exception as e:
            logger.error(f"Error in volatility filter prediction: {e}")
            return 1, 0.5  # Default to safe with low confidence on error
    
    def is_safe_to_trade(self, ohlcv_data: pd.DataFrame, symbol: str = "XAUUSD", 
                        threshold: Optional[float] = None) -> bool:
        """
        Simple boolean check if it's safe to trade
        
        Args:
            ohlcv_data: OHLCV market data
            symbol: Trading symbol
            threshold: Confidence threshold (uses config default if None)
            
        Returns:
            True if safe to trade, False otherwise
        """
        prediction, confidence = self.predict(ohlcv_data, symbol)
        threshold = threshold or settings.volatility_threshold
        
        # Safe to trade if prediction is 1 AND confidence is above threshold
        is_safe = prediction == 1 and confidence >= threshold
        
        logger.info(f"Market safety check: {'SAFE' if is_safe else 'RISKY'} "
                   f"(prediction={prediction}, confidence={confidence:.3f}, threshold={threshold})")
        
        return is_safe
    
    def train(self, training_data: pd.DataFrame, model_type: str = "random_forest") -> Dict[str, float]:
        """
        Train the volatility filter model
        
        Args:
            training_data: DataFrame with features and 'is_safe' target column
            model_type: Type of model to train ('random_forest', 'xgboost', 'logistic')
            
        Returns:
            Dictionary with training metrics
        """
        try:
            logger.info(f"Training volatility filter with {len(training_data)} samples")
            
            # Prepare features and target
            feature_columns = [col for col in self.feature_names if col in training_data.columns]
            missing_features = set(self.feature_names) - set(feature_columns)
            
            if missing_features:
                logger.warning(f"Missing features in training data: {missing_features}")
            
            X = training_data[feature_columns].values
            y = training_data['is_safe'].values
            
            # Handle missing values
            X = np.nan_to_num(X, nan=0.0, posinf=1e6, neginf=-1e6)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train model
            if model_type == "random_forest":
                self.model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42,
                    n_jobs=-1
                )
            elif model_type == "xgboost":
                self.model = xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    n_jobs=-1
                )
            else:
                from sklearn.linear_model import LogisticRegression
                self.model = LogisticRegression(random_state=42, max_iter=1000)
            
            # Fit model
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate
            train_score = self.model.score(X_train_scaled, y_train)
            test_score = self.model.score(X_test_scaled, y_test)
            
            # Cross-validation
            cv_scores = cross_val_score(self.model, X_train_scaled, y_train, cv=5)
            
            # Predictions for detailed metrics
            y_pred = self.model.predict(X_test_scaled)
            
            logger.info(f"Training completed:")
            logger.info(f"  Train accuracy: {train_score:.3f}")
            logger.info(f"  Test accuracy: {test_score:.3f}")
            logger.info(f"  CV accuracy: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
            
            # Save model
            self._save_model()
            
            return {
                'train_accuracy': train_score,
                'test_accuracy': test_score,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'n_samples': len(training_data),
                'n_features': len(feature_columns)
            }
            
        except Exception as e:
            logger.error(f"Error training volatility filter: {e}")
            raise
    
    def create_training_dataset(self, market_data: pd.DataFrame, 
                              labels: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Create training dataset from market data
        
        Args:
            market_data: Historical OHLCV data
            labels: Optional labels DataFrame with 'timestamp' and 'is_safe' columns
            
        Returns:
            Training dataset with features and labels
        """
        try:
            logger.info("Creating training dataset from market data")
            
            training_data = []
            
            # Process data in chunks to extract features
            window_size = self.feature_extractor.lookback_periods
            
            for i in range(window_size, len(market_data)):
                # Get window of data
                window_data = market_data.iloc[i-window_size:i+1].copy()
                
                try:
                    # Extract features
                    features = self.feature_extractor.extract_features(window_data)
                    features['timestamp'] = market_data.iloc[i]['timestamp']
                    
                    # Add default label if not provided
                    if labels is not None:
                        # Find matching label by timestamp
                        timestamp = market_data.iloc[i]['timestamp']
                        matching_labels = labels[labels['timestamp'] == timestamp]
                        if not matching_labels.empty:
                            features['is_safe'] = matching_labels.iloc[0]['is_safe']
                        else:
                            continue  # Skip if no label found
                    else:
                        # Default labeling strategy (you should replace this with actual labels)
                        features['is_safe'] = self._default_labeling_strategy(window_data)
                    
                    training_data.append(features)
                    
                except Exception as e:
                    logger.warning(f"Error processing window at index {i}: {e}")
                    continue
            
            if not training_data:
                raise ValueError("No training data could be created")
            
            df = pd.DataFrame(training_data)
            logger.info(f"Created training dataset with {len(df)} samples")
            
            return df
            
        except Exception as e:
            logger.error(f"Error creating training dataset: {e}")
            raise
    
    def _default_labeling_strategy(self, window_data: pd.DataFrame) -> int:
        """
        Default labeling strategy for training data
        This is a simple heuristic - you should replace with actual market analysis
        """
        try:
            close = window_data['close'].values
            high = window_data['high'].values
            low = window_data['low'].values
            
            # Calculate some basic volatility metrics
            price_range = (high[-1] - low[-1]) / close[-1]
            price_change = abs(close[-1] - close[-2]) / close[-2] if len(close) > 1 else 0
            
            # Simple rule: if volatility is too high, mark as risky
            if price_range > 0.02 or price_change > 0.015:  # 2% range or 1.5% change
                return 0  # Risky
            else:
                return 1  # Safe
                
        except Exception:
            return 1  # Default to safe
    
    def _save_model(self):
        """Save trained model and scaler"""
        try:
            # Ensure models directory exists
            Path("models").mkdir(exist_ok=True)
            
            # Save model
            if self.model is not None:
                joblib.dump(self.model, self.model_path)
                logger.info(f"Model saved to {self.model_path}")
            
            # Save scaler
            if self.scaler is not None:
                joblib.dump(self.scaler, self.scaler_path)
                logger.info(f"Scaler saved to {self.scaler_path}")
                
        except Exception as e:
            logger.error(f"Error saving model: {e}")
    
    def _load_model(self):
        """Load trained model and scaler"""
        try:
            # Load model
            if Path(self.model_path).exists():
                self.model = joblib.load(self.model_path)
                logger.info(f"Model loaded from {self.model_path}")
            
            # Load scaler
            if Path(self.scaler_path).exists():
                self.scaler = joblib.load(self.scaler_path)
                logger.info(f"Scaler loaded from {self.scaler_path}")
                
        except Exception as e:
            logger.warning(f"Could not load existing model: {e}")
            self.model = None
            self.scaler = None
