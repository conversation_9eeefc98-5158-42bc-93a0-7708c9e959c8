"""
Feature extraction for AI volatility filter
Extracts technical indicators and market features for ML model input
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timezone
import ta

from config import TradingConfig
from src.ai_trading.utils.logger import get_logger

logger = get_logger(__name__)


class FeatureExtractor:
    """Extract features for volatility filtering and signal generation"""
    
    def __init__(self, lookback_periods: int = 20):
        self.lookback_periods = lookback_periods
        
    def extract_features(self, ohlcv_data: pd.DataFrame, symbol: str = "XAUUSD") -> Dict[str, float]:
        """
        Extract all features for the volatility filter
        
        Args:
            ohlcv_data: DataFrame with columns ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            symbol: Trading symbol
            
        Returns:
            Dictionary of extracted features
        """
        try:
            if len(ohlcv_data) < self.lookback_periods:
                raise ValueError(f"Insufficient data: need {self.lookback_periods}, got {len(ohlcv_data)}")
            
            # Ensure we have the required columns
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in ohlcv_data.columns for col in required_cols):
                raise ValueError(f"Missing required columns: {required_cols}")
            
            features = {}
            
            # Price-based features
            features.update(self._extract_price_features(ohlcv_data))
            
            # Volatility features
            features.update(self._extract_volatility_features(ohlcv_data))
            
            # Volume features
            features.update(self._extract_volume_features(ohlcv_data))
            
            # Technical indicator features
            features.update(self._extract_technical_features(ohlcv_data))
            
            # Time-based features
            features.update(self._extract_time_features())
            
            # Market structure features
            features.update(self._extract_market_structure_features(ohlcv_data))
            
            logger.debug(f"Extracted {len(features)} features for {symbol}")
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            raise
    
    def _extract_price_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Extract price-based features"""
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        return {
            'price_change_pct': (close[-1] - close[-2]) / close[-2] * 100 if len(close) > 1 else 0.0,
            'price_range_pct': (high[-1] - low[-1]) / close[-1] * 100,
            'hl_ratio': high[-1] / low[-1] if low[-1] > 0 else 1.0,
        }
    
    def _extract_volatility_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Extract volatility-based features"""
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        
        # ATR (Average True Range)
        atr_14 = ta.volatility.average_true_range(
            high=pd.Series(high), 
            low=pd.Series(low), 
            close=pd.Series(close), 
            window=14
        ).iloc[-1]
        
        # Bollinger Bands
        bb_high, bb_mid, bb_low = ta.volatility.bollinger_hband(close=pd.Series(close)), \
                                   ta.volatility.bollinger_mavg(close=pd.Series(close)), \
                                   ta.volatility.bollinger_lband(close=pd.Series(close))
        
        bb_width = (bb_high.iloc[-1] - bb_low.iloc[-1]) / bb_mid.iloc[-1] * 100
        bb_position = (close[-1] - bb_low.iloc[-1]) / (bb_high.iloc[-1] - bb_low.iloc[-1])
        
        # Standard deviation
        std_dev_20 = pd.Series(close).rolling(window=20).std().iloc[-1]
        std_dev_pct = std_dev_20 / close[-1] * 100
        
        return {
            'atr_14': atr_14,
            'bb_width': bb_width,
            'bb_position': bb_position,
            'std_dev_20': std_dev_20,
            'std_dev_pct': std_dev_pct,
        }
    
    def _extract_volume_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Extract volume-based features"""
        volume = df['volume'].values
        
        if len(volume) < 10:
            return {
                'volume_avg_10': volume[-1] if len(volume) > 0 else 0.0,
                'volume_ratio': 1.0,
                'volume_trend': 0.0,
            }
        
        volume_avg_10 = np.mean(volume[-10:])
        volume_ratio = volume[-1] / volume_avg_10 if volume_avg_10 > 0 else 1.0
        
        # Volume trend (slope of last 5 periods)
        if len(volume) >= 5:
            x = np.arange(5)
            y = volume[-5:]
            volume_trend = np.polyfit(x, y, 1)[0]  # Slope
        else:
            volume_trend = 0.0
        
        return {
            'volume_avg_10': volume_avg_10,
            'volume_ratio': volume_ratio,
            'volume_trend': volume_trend,
        }
    
    def _extract_technical_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Extract technical indicator features"""
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        close_series = pd.Series(close)
        high_series = pd.Series(high)
        low_series = pd.Series(low)
        
        # RSI
        rsi_14 = ta.momentum.rsi(close=close_series, window=14).iloc[-1]
        
        # Moving averages
        ma_20 = close_series.rolling(window=20).mean().iloc[-1]
        ma_50 = close_series.rolling(window=50).mean().iloc[-1] if len(close) >= 50 else ma_20
        
        # Price distance to moving average
        price_distance_to_ma = (close[-1] - ma_20) / ma_20 * 100
        
        # MACD
        macd_line = ta.trend.macd(close=close_series).iloc[-1]
        macd_signal = ta.trend.macd_signal(close=close_series).iloc[-1]
        macd_histogram = macd_line - macd_signal
        
        # Stochastic
        stoch_k = ta.momentum.stoch(high=high_series, low=low_series, close=close_series).iloc[-1]
        
        return {
            'rsi_14': rsi_14,
            'price_distance_to_ma': price_distance_to_ma,
            'ma_20': ma_20,
            'ma_50': ma_50,
            'macd_histogram': macd_histogram,
            'stoch_k': stoch_k,
        }
    
    def _extract_time_features(self) -> Dict[str, float]:
        """Extract time-based features"""
        now = datetime.now(timezone.utc)
        
        return {
            'hour_of_day': float(now.hour),
            'day_of_week': float(now.weekday()),  # 0=Monday, 6=Sunday
            'is_weekend': float(now.weekday() >= 5),  # Saturday=5, Sunday=6
        }
    
    def _extract_market_structure_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """Extract market structure features"""
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        if len(close) < 10:
            return {
                'higher_highs': 0.0,
                'lower_lows': 0.0,
                'trend_strength': 0.0,
            }
        
        # Count higher highs and lower lows in recent periods
        recent_highs = high[-10:]
        recent_lows = low[-10:]
        
        higher_highs = sum(1 for i in range(1, len(recent_highs)) if recent_highs[i] > recent_highs[i-1])
        lower_lows = sum(1 for i in range(1, len(recent_lows)) if recent_lows[i] < recent_lows[i-1])
        
        # Trend strength (linear regression slope)
        if len(close) >= 20:
            x = np.arange(20)
            y = close[-20:]
            trend_strength = np.polyfit(x, y, 1)[0]  # Slope
        else:
            trend_strength = 0.0
        
        return {
            'higher_highs': float(higher_highs),
            'lower_lows': float(lower_lows),
            'trend_strength': trend_strength,
        }
    
    def get_feature_names(self) -> List[str]:
        """Get list of all feature names"""
        return [
            # Price features
            'price_change_pct', 'price_range_pct', 'hl_ratio',
            # Volatility features
            'atr_14', 'bb_width', 'bb_position', 'std_dev_20', 'std_dev_pct',
            # Volume features
            'volume_avg_10', 'volume_ratio', 'volume_trend',
            # Technical features
            'rsi_14', 'price_distance_to_ma', 'ma_20', 'ma_50', 'macd_histogram', 'stoch_k',
            # Time features
            'hour_of_day', 'day_of_week', 'is_weekend',
            # Market structure features
            'higher_highs', 'lower_lows', 'trend_strength',
        ]
