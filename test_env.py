#!/usr/bin/env python3
"""
Test environment variable loading
"""
import os
from dotenv import load_dotenv

# Load environment variables explicitly
load_dotenv('.env', override=True)

print("Environment Variables Test:")
print("=" * 50)

token = os.getenv('METAAPI_TOKEN')
account_id = os.getenv('MASTER_ACCOUNT_ID')
password = os.getenv('MASTER_ACCOUNT_PASSWORD')
server = os.getenv('MASTER_ACCOUNT_SERVER')

print(f"Token loaded: {'Yes' if token else 'No'}")
if token:
    print(f"Token length: {len(token)}")
    print(f"Token starts with: {token[:50]}...")
    print(f"Token ends with: ...{token[-50:]}")

print(f"Account ID: {account_id}")
print(f"Password: {password}")
print(f"Server: {server}")

# Test if it's a JWT token
if token and token.startswith('eyJ'):
    print("✅ Token appears to be a JWT token")
    
    # Try to decode the header
    try:
        import base64
        import json
        
        # JWT tokens have 3 parts separated by dots
        parts = token.split('.')
        if len(parts) == 3:
            # Decode the header (first part)
            header_data = parts[0]
            # Add padding if needed
            header_data += '=' * (4 - len(header_data) % 4)
            header = json.loads(base64.b64decode(header_data))
            print(f"JWT Header: {header}")
            
            # Decode the payload (second part)
            payload_data = parts[1]
            payload_data += '=' * (4 - len(payload_data) % 4)
            payload = json.loads(base64.b64decode(payload_data))
            print(f"JWT User ID: {payload.get('_id', 'N/A')}")
            print(f"JWT Token ID: {payload.get('tokenId', 'N/A')}")
            print(f"JWT Issued At: {payload.get('iat', 'N/A')}")
            
            # Check access rules
            access_rules = payload.get('accessRules', [])
            print(f"Access Rules: {len(access_rules)} rules")
            for rule in access_rules[:3]:  # Show first 3 rules
                print(f"  - {rule.get('id', 'N/A')}: {rule.get('roles', [])}")
                
    except Exception as e:
        print(f"⚠️ Could not decode JWT: {e}")
else:
    print("❌ Token does not appear to be a JWT token")
