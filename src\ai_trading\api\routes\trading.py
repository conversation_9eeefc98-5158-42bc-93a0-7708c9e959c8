"""
Trading routes
"""
from fastapi import APIRouter

router = APIRouter()


@router.get("/signals")
async def get_trading_signals():
    """Get current trading signals"""
    # TODO: Implement trading signals endpoint
    return {"message": "Trading signals - TODO"}


@router.get("/trades")
async def get_active_trades():
    """Get active trades"""
    # TODO: Implement active trades endpoint
    return {"message": "Active trades - TODO"}
