#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to test the trained volatility filter
"""
import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.ai_trading.models.volatility_filter import VolatilityFilter
from src.ai_trading.utils.logger import setup_logging

def create_test_scenarios():
    """Create different market scenarios for testing"""
    scenarios = {}
    
    # Scenario 1: Low volatility (should be SAFE)
    low_vol_data = []
    base_price = 1800
    for i in range(25):
        price = base_price + np.random.normal(0, 2)  # Low volatility
        low_vol_data.append({
            'timestamp': datetime.now() - timedelta(minutes=5*i),
            'open': price + np.random.uniform(-0.5, 0.5),
            'high': price + np.random.uniform(0, 1),
            'low': price - np.random.uniform(0, 1),
            'close': price,
            'volume': np.random.uniform(2000, 3000)
        })
    scenarios['Low Volatility'] = pd.DataFrame(low_vol_data[::-1])
    
    # Scenario 2: High volatility (should be RISKY)
    high_vol_data = []
    for i in range(25):
        price = base_price + np.random.normal(0, 20)  # High volatility
        high_vol_data.append({
            'timestamp': datetime.now() - timedelta(minutes=5*i),
            'open': price + np.random.uniform(-5, 5),
            'high': price + np.random.uniform(0, 15),
            'low': price - np.random.uniform(0, 15),
            'close': price,
            'volume': np.random.uniform(8000, 12000)  # High volume
        })
    scenarios['High Volatility'] = pd.DataFrame(high_vol_data[::-1])
    
    # Scenario 3: Normal market conditions
    normal_data = []
    for i in range(25):
        price = base_price + np.random.normal(0, 5)  # Normal volatility
        normal_data.append({
            'timestamp': datetime.now() - timedelta(minutes=5*i),
            'open': price + np.random.uniform(-1, 1),
            'high': price + np.random.uniform(0, 3),
            'low': price - np.random.uniform(0, 3),
            'close': price,
            'volume': np.random.uniform(3000, 5000)
        })
    scenarios['Normal Market'] = pd.DataFrame(normal_data[::-1])
    
    # Scenario 4: Trending market (should be SAFE)
    trend_data = []
    for i in range(25):
        price = base_price + i * 2  # Steady uptrend
        trend_data.append({
            'timestamp': datetime.now() - timedelta(minutes=5*i),
            'open': price + np.random.uniform(-0.5, 0.5),
            'high': price + np.random.uniform(0, 2),
            'low': price - np.random.uniform(0, 1),
            'close': price,
            'volume': np.random.uniform(2500, 4000)
        })
    scenarios['Trending Market'] = pd.DataFrame(trend_data[::-1])
    
    return scenarios

def main():
    """Test the volatility filter with different scenarios"""
    setup_logging()
    
    print("🧪 AI Forex Trading Platform - Volatility Filter Testing")
    print("=" * 60)
    
    # Initialize volatility filter
    volatility_filter = VolatilityFilter()
    
    # Check if model exists
    if volatility_filter.model is None:
        print("❌ No trained model found!")
        print("Please run: python scripts/train_volatility_filter.py")
        sys.exit(1)
    
    print("✅ Loaded trained volatility filter model")
    print()
    
    # Create test scenarios
    scenarios = create_test_scenarios()
    
    print("🔍 Testing different market scenarios:")
    print("-" * 40)
    
    results = []
    
    for scenario_name, data in scenarios.items():
        try:
            # Test prediction
            prediction, confidence = volatility_filter.predict(data, symbol="XAUUSD")
            is_safe = volatility_filter.is_safe_to_trade(data, symbol="XAUUSD")
            
            # Store results
            result = {
                'scenario': scenario_name,
                'prediction': 'SAFE' if prediction == 1 else 'RISKY',
                'confidence': confidence,
                'trade_allowed': 'YES' if is_safe else 'NO'
            }
            results.append(result)
            
            # Print result
            status_emoji = "✅" if prediction == 1 else "⚠️"
            trade_emoji = "🟢" if is_safe else "🔴"
            
            print(f"{status_emoji} {scenario_name:15} | "
                  f"Prediction: {result['prediction']:5} | "
                  f"Confidence: {confidence:.3f} | "
                  f"Trade: {trade_emoji} {result['trade_allowed']}")
            
        except Exception as e:
            print(f"❌ Error testing {scenario_name}: {e}")
    
    print()
    print("📊 Test Summary:")
    print("-" * 20)
    
    safe_predictions = sum(1 for r in results if r['prediction'] == 'SAFE')
    risky_predictions = sum(1 for r in results if r['prediction'] == 'RISKY')
    trades_allowed = sum(1 for r in results if r['trade_allowed'] == 'YES')
    
    print(f"Safe predictions: {safe_predictions}/{len(results)}")
    print(f"Risky predictions: {risky_predictions}/{len(results)}")
    print(f"Trades allowed: {trades_allowed}/{len(results)}")
    
    avg_confidence = np.mean([r['confidence'] for r in results])
    print(f"Average confidence: {avg_confidence:.3f}")
    
    print()
    print("💡 Expected Results:")
    print("- Low Volatility: SAFE ✅")
    print("- High Volatility: RISKY ⚠️")
    print("- Normal Market: SAFE ✅")
    print("- Trending Market: SAFE ✅")
    
    print()
    print("🎯 Model Performance:")
    if results:
        # Simple heuristic check
        expected_safe = ['Low Volatility', 'Normal Market', 'Trending Market']
        expected_risky = ['High Volatility']
        
        correct_predictions = 0
        for result in results:
            if (result['scenario'] in expected_safe and result['prediction'] == 'SAFE') or \
               (result['scenario'] in expected_risky and result['prediction'] == 'RISKY'):
                correct_predictions += 1
        
        accuracy = correct_predictions / len(results)
        print(f"Scenario accuracy: {accuracy:.1%}")
        
        if accuracy >= 0.75:
            print("🎉 Model performance looks good!")
        else:
            print("⚠️ Model may need retraining or parameter adjustment")
    
    print()
    print("✅ Testing complete!")

if __name__ == "__main__":
    main()
